__Data scrapped on October 9th, 2025 from the official doc ()__

# JavaScript Software Development Kit for Freemius

Freemius offers a JavaScript (JS) SDK written in TypeScript to help you integrate Freemius into your SaaS applications. At a glance it has the following features:

* **API Client**: A fully typed API client to interact with the [Freemius API](https://docs.freemius.com/api).
* **Checkout**: Easily create and manage Checkout [options](https://freemius.com/help/help/documentation/checkout/freemius-checkout-buy-button/.md) and [links](https://freemius.com/help/help/documentation/checkout/hosted-checkout/.md) for your application. Also has convenience methods to process redirections from the [Hosted Freemius Checkout](https://freemius.com/help/help/documentation/checkout/hosted-checkout/.md#redirection-after-a-successful-purchase).
* **Webhooks**: Create webhook listners for specific events and process incoming [webhook requests](https://freemius.com/help/help/documentation/saas/events-webhooks/.md) from Freemius.
* **Pricing**: Easily retrieve data for custom pricing tables within your software.
* **Purchase**: Securely process and manage purchases made through Freemius.

The SDK works for any JavaScript runtime platform including [Node.js](https://nodejs.org/en), [Bun](https://bun.com/), [Deno](https://deno.com/) etc. Please read the guides to get started.

Backend Use Only

The Freemius JS/TS SDK is intended for **backend use only**. It should not be used in frontend or client-side applications (browsers), as it requires access to sensitive credentials such as the Secret Key and Bearer Token. Exposing these credentials in a client-side environment can lead to security vulnerabilities and unauthorized access to your Freemius account.

# Installation Guide for the Freemius JS/TS SDK

This installation guide will help you get up and running with the Freemius TypeScript/JavaScript SDK in your SaaS application.

## Prerequisites[â€‹](#prerequisites "Direct link to Prerequisites")

* Node.js, version 20 or higher or other platforms like Deno or Bun.
* A package manager: npm, yarn, or pnpm

We also recommend using TypeScript for type safety and an improved development experience; however, you can use the SDK in plain JavaScript projects as well.

You will also need to have a Freemius account and a product set up. If you have not done so already, please follow the [getting started guide](https://freemius.com/help/help/documentation/saas/saas-plans-pricing/.md).

note

Support for Bun and Deno is experimental. Please [report any issues](https://github.com/Freemius/freemius-js/issues) you encounter.

## Installation[â€‹](#installation "Direct link to Installation")

Depending on your preferred package manager, run one of the following commands in your project directory:

* npm
* Yarn
* pnpm
* Bun

```
npm install @freemius/sdk @freemius/checkout zod
```

```
yarn add @freemius/sdk @freemius/checkout zod
```

```
pnpm add @freemius/sdk @freemius/checkout zod
```

```
bun add @freemius/sdk @freemius/checkout zod
```

note

* [`@freemius/checkout`](https://github.com/Freemius/freemius-checkout-js/) is required for the checkout generation feature. You can also use it on your frontend to render the checkout modal.
* [`zod`](https://zod.dev/) is required for schema validation.

## Retrieving Keys from the Developer Dashboard[â€‹](#retrieving-keys-from-the-developer-dashboard "Direct link to Retrieving Keys from the Developer Dashboard")

Next, please head over to the [Developer Dashboard](https://dashboard.freemius.com/) and navigate to the **Settings** page for your product and selec the **API & Keys** tab.

![](/help/assets/ideal-img/retrieve-keys-for-sdk.3f8d713.480.png)

Now scroll down to the **Usage examples**, copy the code snippet written in the `.env` format.

![](/help/assets/ideal-img/retrieve-env-file-snippet.b9f9643.480.png)

You will need these keys to configure the SDK in your application. We recommend storing them in environment variables using the standard `.env` file format.

Keep Your Keys Secure

Make sure to keep your Secret Key and Bearer Token secure and do not expose them in client-side code or public repositories.

## Configuration[â€‹](#configuration "Direct link to Configuration")

We recommend creating an instance of the Freemius SDK in a separate module and exporting it for use throughout your application.

src/lib/freemius.ts

```
import { Freemius } from '@freemius/sdk';

export const freemius = new Freemius({
  productId: process.env.FREEMIUS_PRODUCT_ID!,
  apiKey: process.env.FREEMIUS_API_KEY!,
  secretKey: process.env.FREEMIUS_SECRET_KEY!,
  publicKey: process.env.FREEMIUS_PUBLIC_KEY!,
});
```

To use it, simply import the `freemius` instance from the module you created.

src/app.ts

```
import { freemius } from './lib/freemius';

async function main() {
  const pricing = await freemius.pricing.retrieve();
  console.log(pricing);
}
```

Please continue reading to learn more about the different features of the SDK and how to use them in your application.

# Integrating the Freemius JS SDK into your Application

Most SaaS applications have unique stacks and architectures. However, after analyzing common integration patterns, we have developed a straightforward guide that should work for the majority of applications.

In this integration guide, we will:

1. Create a local database table to store Freemius purchase information (in an entitlement table).
2. Demonstrate how to create Checkout Options in the backend to easily generate checkouts in the frontend.
3. Process a purchase and store the relevant information in your local database.
4. Synchronize license updates via [webhooks](https://freemius.com/help/help/documentation/saas/events-webhooks/.md).

note

Refer to the [installation guide](https://freemius.com/help/help/documentation/saas-sdk/js-sdk/installation/.md) if you have not set up the SDK yet.

This guide does not assume any specific framework or architecture.

* We use TypeScript, but you can easily adapt the examples to JavaScript.
* We use a generic `db` object to represent database operations. Replace it with your actual database library or ORM, such as Prisma or Drizzle. We provide examples for popular ORMs.
* We use a generic `fetch`-based API to illustrate how to create API endpoints. You can substitute this with your framework's routing and request handling mechanism, such as Express, Next.js, or Fastify.

tip

Using Next.js? See our [Next.js integration guide](https://freemius.com/help/help/documentation/saas-sdk/framework/nextjs/.md) for a more tailored approach.

## Creating the Entitlement Table[â€‹](#creating-the-entitlement-table "Direct link to Creating the Entitlement Table")

A license is the primary entitlement in Freemius. It represents a purchase made by a customer for a specific product, plan and pricing. A license may be associated with a subscription, which defines the billing cycle and renewal terms. For one-time purchases, the license will not be linked to any subscription.

We recommend creating a local `user_fs_entitlement` table in your database to store relevant information about each license. Below is an example schema:

* SQL
* Prisma
* Drizzle

```
-- First create the enum type
CREATE TYPE fs_entitlement_type AS ENUM ('subscription', 'lifetime');

-- Then create the table
CREATE TABLE user_fs_entitlement (
    id            TEXT PRIMARY KEY,
    "userId"      TEXT NOT NULL,
    "fsLicenseId" TEXT NOT NULL UNIQUE,
    "fsPlanId"    TEXT NOT NULL,
    "fsPricingId" TEXT NOT NULL,
    "fsUserId"    TEXT NOT NULL,
    type          fs_entitlement_type NOT NULL,
    expiration    TIMESTAMP(3) WITHOUT TIME ZONE,
    "isCanceled"  BOOLEAN NOT NULL,
    "createdAt"   TIMESTAMP(3) WITHOUT TIME ZONE NOT NULL,
    CONSTRAINT    fk_user FOREIGN KEY ("userId") REFERENCES "User"(id) ON DELETE CASCADE
);

-- Index on type for faster filtering
CREATE INDEX idx_user_fs_entitlement_type ON user_fs_entitlement (type);
```

```
// For the sake of this example, we are using a single active subscription license per user.
// But the schema can handle multiple licenses per user.
model UserFsEntitlement {
    id     String @id @default(cuid())
    userId String
    User   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

    // The following matches the PurchaseDBData from Freemius Node SDK.
    // You can also use these fields as BigInt if you prefer, and in that case you will need to convert the String to BigInt in your application.
    fsLicenseId String            @unique
    fsPlanId    String
    fsPricingId String
    fsUserId    String
    type        FsEntitlementType
    expiration  DateTime?
    isCanceled  Boolean
    createdAt   DateTime

    // Add the index to optimize queries filtering by type (as needed for the JS SDK).
    @@index(type)
    @@map("user_fs_entitlement")
}
```

```
import {
  pgTable,
  text,
  boolean,
  timestamp,
  index,
  pgEnum,
} from 'drizzle-orm/pg-core';
import { users } from './user'; // your User table

// Enum type
export const fsEntitlementType = pgEnum('fs_entitlement_type', [
  'subscription',
  'lifetime',
]);

// Table
export const userFsEntitlements = pgTable(
  'user_fs_entitlement',
  {
    id: text('id').primaryKey(),

    userId: text('userId')
      .notNull()
      .references(() => users.id, { onDelete: 'cascade' }),

    fsLicenseId: text('fsLicenseId').notNull().unique(),
    fsPlanId: text('fsPlanId').notNull(),
    fsPricingId: text('fsPricingId').notNull(),
    fsUserId: text('fsUserId').notNull(),

    type: fsEntitlementType('type').notNull(),

    expiration: timestamp('expiration', { withTimezone: false, precision: 3 }),
    isCanceled: boolean('isCanceled').notNull(),
    createdAt: timestamp('createdAt', {
      withTimezone: false,
      precision: 3,
    }).notNull(),
  },
  (table) => ({
    typeIdx: index('idx_user_fs_entitlement_type').on(table.type),
  })
);
```

## Creating Checkouts[â€‹](#creating-checkouts "Direct link to Creating Checkouts")

We recommend using the backend to create Checkouts. This lets you easily scope the Checkout to a specific user, pre-fill customer information, and apply discounts or trials.

```
const checkout = await freemius.checkout.create({
  user: session?.user,
  isSandbox: process.env.NODE_ENV !== 'production',
});
```

Here we assume you have a `session` object that contains the authenticated user's information. The `user` property should include at least the `email`, and `name` or `firstName` and `lastName` fields.

![](/help/assets/ideal-img/checkout-readonly-user.5217dbd.480.png)

With the `isSandbox` flag set to `true`, the checkout will be created in sandbox mode, which is useful for testing purposes. In production, you should set it to `false`. For more information please refer to the [checkout documentation](https://freemius.com/help/help/documentation/saas-sdk/js-sdk/checkout/.md).

How you pass this information from the backend to the frontend depends on your framework. You will want to use the following method to generate the checkout options:

```
const options = checkout.getOptions();
const link = checkout.getLink();
```

We also have a handy `serialize` method that combines both:

```
const { options, link } = checkout.serialize();
```

For the sake of this example, we will assume that the `options` object is now available in the frontend as `window.__FS_CHECKOUT_OPTIONS__`.

You can now use it to create a [checkout](https://freemius.com/help/help/documentation/checkout/freemius-checkout-buy-button/.md) in the frontend:

```
// Front-end code
import { Checkout } from '@freemius/checkout';

const checkout = new Checkout(window.__FS_CHECKOUT_OPTIONS__);

checkout.open({
  success: (data) => {
    console.log('Purchase completed:', data);
  },
});
```

info

Please note that the example above uses the [Freemius Checkout JS SDK](https://github.com/Freemius/freemius-checkout-js/), which is not to be confused with the Freemius JS SDK.

The **Checkout SDK** is specifically designed to handle the checkout process in the frontend, while the Freemius JS SDK is used for backend operations such as creating checkout options, managing licenses, and handling subscriptions.

## Sending Purchase Data to the Backend[â€‹](#sending-purchase-data-to-the-backend "Direct link to Sending Purchase Data to the Backend")

Next, we want to send purchase data back to the backend to store it in our local database. To do this, we will use the `success` callback of the checkout.

```
checkout.open({
  success: async (data) => {
    console.log('Purchase completed:', data);
    await fetch('/api/purchase', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });
  },
});
```

Now, on the backend, we can create an API endpoint to handle this request.

First, we will create a convenient function, `processPurchaseInfo`, to process the purchase data and store it in our local database.

user-entitlement.ts

As a convention, let's create all entitlement related functions in a file named `user-entitlement.ts`.

src/lib/user-entitlement.ts

```
import { type PurchaseInfo } from '@freemius/sdk';
import { freemius } from './freemius'; // our freemius instance
import { db } from './db'; // our database instance

export async function findUserByEmail(email: string) {
  // Replace with your actual user lookup logic
  return db.user.findUnique({ where: { email } });
}

export async function processPurchaseInfo(purchase: PurchaseInfo) {
  const user = await findUserByEmail(purchase.email);

  // We exit if the user hasn't registered to our application yet.
  // Alternatively, you can register the user automatically here if desired.
  if (!user) {
    return;
  }

  // Insert or update the purchase in our local database
  await db.userFsEntitlement.upsert({
    where: {
      fsLicenseId: fsPurchase.licenseId,
    },
    update: fsPurchase.toEntitlementRecord(),
    create: fsPurchase.toEntitlementRecord({ userId: user.id }),
  });
}
```

Finally, we can create the API endpoint to handle the purchase data.

```
import { freemius } from './lib/freemius'; // our freemius instance
import { db } from './lib/db'; // our database instance
import { processPurchaseInfo } from './lib/user-entitlement';

export default {
  async fetch(request: Request) {
    if (request.method !== 'POST') {
      return new Response('Method Not Allowed', { status: 405 });
    }

    const purchaseData = await request.json();

    // Validate the purchase data
    if (!purchaseData.purchase?.license_id) {
      return new Response('Bad Request: Missing licenseId', { status: 400 });
    }

    try {
      // Retrieve the full purchase details from Freemius
      const licenseId = purchaseData.purchase.license_id;
      const purchase = await freemius.purchase.retrievePurchase(licenseId);

      await processPurchaseInfo(purchase);

      return new Response('Purchase recorded', { status: 200 });
    } catch (error) {
      console.error('Error processing purchase:', error);
      return new Response('Internal Server Error', { status: 500 });
    }
  },
};
```

The example above shows a serverless function that uses the Fetch API. You can adapt it to your framework's routing and request handling mechanism.

Using the React Starter Kit?

You can just implement the needed endpoints and use the pre-built UI components. See the [Starter Kit API Endpoints guide](https://freemius.com/help/help/documentation/saas-sdk/js-sdk/starter-kit-api-endpoints/.md) for more details.

## Using Entitlement Logic[â€‹](#using-entitlement-logic "Direct link to Using Entitlement Logic")

Finally, we can create a function to check if a user has an active entitlement.

src/lib/user-entitlement.ts

```
import { freemius } from './lib/freemius'; // our freemius instance
import { db, type UserFsEntitlement } from './lib/db'; // our database instance

// ... Existing code ...

/**
 * Get the user's entitlement.
 *
 * @returns The user's active entitlement or null if the user does not have an active entitlement.
 */
export async function getUserEntitlement(
  userId: string
): Promise<UserFsEntitlement | null> {
  const entitlements = await db.userFsEntitlement.findMany({
    where: { userId, type: 'subscription' },
  });

  return freemius.entitlement.getActive(entitlements);
}
```

The `entitlement.getActive` method will check if the license is still valid, not expired, and not canceled. If the license is valid, it will return the license data; otherwise, it will return `null`. You can read more about it in the [purchase documentation](https://freemius.com/help/help/documentation/saas-sdk/js-sdk/purchases/.md).

Now you can use this function to check if a user has an active entitlement and grant or restrict access to your product accordingly. We recommend using the `fsPricingId` field to determine the level of access, as it is unique for each pricing plan.

```
export function hasAccessToFeatureX(
  entitlement: UserFsEntitlement | null
): boolean {
  if (!entitlement) {
    return false;
  }

  const requiredPricingId = 'your_required_pricing_id_here';

  return entitlement.fsPricingId === requiredPricingId;
}
```

## Handling License Updates via Webhooks[â€‹](#handling-license-updates-via-webhooks "Direct link to Handling License Updates via Webhooks")

We need to handle license updates that may occur outside of our application, such as cancellations, renewals, or expirations. For this, we will use webhooks.

Do not avoid setting up webhooks

If you do not set up webhooks, your application will not be able to update the license's expiration date or cancellation status when they change in Freemius. This may lead to users losing access to your product even though they have an active subscription.

We will set up a webhook listener at `/api/webhook` to process incoming webhook events from Freemius.

src/api/webhook.ts

```
import { freemius } from './lib/freemius'; // our freemius instance
import { type LicenseEntity, type WebhookEventType } from '@freemius/sdk';
import { processPurchaseInfo } from './lib/user-entitlement';

async function syncEntitlementFromWebhook(licenseId: string) {
  const purchaseInfo = await freemius.purchase.retrievePurchase(licenseId);

  if (purchaseInfo) {
    await processPurchaseInfo(purchaseInfo);
  }
}

export default {
  async fetch(request: Request) {
    if (request.method !== 'POST') {
      return new Response('Method Not Allowed', { status: 405 });
    }

    const listener = freemius.webhook.createListener();

    const licenseEvents: WebhookEventType[] = [
      'license.created',
      'license.extended',
      'license.shortened',
      'license.updated',
      'license.cancelled',
      'license.expired',
      'license.plan.changed',
    ];

    listener.on(licenseEvents, async ({ objects: { license } }) => {
      if (license && license.id) {
        await syncEntitlementFromWebhook(license.id);
      }
    });

    return await freemius.webhook.processFetch(listener, request);
  },
};
```

This will automatically synchronize the license information in your local database whenever a relevant event occurs in Freemius.

From the Freemius [Developer Dashboard](https://dashboard.freemius.com/), you can also delete licenses, which will trigger the `license.deleted` event. To listen for this event, you can add the following code to the entitlement file and webhook listener:

src/lib/user-entitlement.ts

```
// ... Existing code ...

export async function deleteEntitlement(fsLicenseId: string) {
  await db.userFsEntitlement.delete({
    where: { fsLicenseId },
  });
}
```

And update the webhook listener:

src/api/webhook.ts

```

// ... Existing code ...

export default {
  async fetch(request: Request) {
    // ...
    const listener = freemius.webhook.createListener();
    // ... Existing event listeners ...

    listener.on('license.deleted', async ({ data} }) => {
      await deleteEntitlement(data.license_id);
    });

    return await freemius.webhook.processFetch(listener, request);
  },
};
```

Notice that the data structure for the `license.deleted` event is different from the other events, so we need to handle it separately.

Finally, configure the webhook URL in the [Freemius Developer Dashboard](https://freemius.com/help/help/documentation/saas/events-webhooks/.md#how-to-create-a-webhook).

## What's next?[â€‹](#whats-next "Direct link to What's next?")

You have now fully integrated Freemius into your SaaS application. You can explore different features of the SDK to create even richer experiences for your users.

If you are looking for pre-made UI components for Checkout, Paywall, Customer Portal, Pricing Table, and more, please take a look at our [React Starter Kit](https://freemius.com/help/help/documentation/saas-sdk/react-starter/.md).

The starter kit requires setting up two API endpoints, which you can implement using the examples provided [in this guide](https://freemius.com/help/help/documentation/saas-sdk/js-sdk/starter-kit-api-endpoints/.md).

# Creating Webhook Listeners with Freemius JS SDK

The Freemius SDK provides a robust system to listen and process webhook events from the Freemius platform. This guide explains how to set up, configure, and process webhooks in various environments, including Next.js, Node.js, and serverless platforms.

note

Check out the [installation guide](https://freemius.com/help/help/documentation/saas-sdk/js-sdk/installation/.md) if you haven't set up the SDK yet.

## Create a Webhook Listener[â€‹](#create-a-webhook-listener "Direct link to Create a Webhook Listener")

To create a webhook listener, use the `freemius.webhook.createListener` method.

```
const listener = freemius.webhook.createListener();
```

You can create multiple listeners if you want to handle different sets of events separately. However for most use cases, a single listener is sufficient.

## Register Event Handlers[â€‹](#register-event-handlers "Direct link to Register Event Handlers")

The `listener` object exposes an `on` method to register event handlers for specific Freemius events. Each handler receives the event data as a parameter.

```
listener.on('license.created', async ({ objects: { license } }) => {
  await syncLicenseFromWebhook(license);
  console.log('License created:', license);
});

listener.on(
  'subscription.renewal.failed',
  async ({ objects: { subscription } }) => {
    await sendRenewalFailureEmail(subscription);
    console.log('Subscription renewal failed:', subscription);
  }
);
```

The payload of the callback function will be strictly typed based on the event type, providing you with full TypeScript support. You can register multiple handlers for the same event if needed.

tip

You can use TypeScript IntelliSense to explore all available methods on the `listener` object.

Handling Multiple Events

For events that has same payload, you can pass an array of event names to register the same handler for multiple events:

```
listener.on(
  [
    'license.created',
    'license.extended',
    'license.shortened',
    'license.updated',
    'license.cancelled',
    'license.expired',
    'license.plan.changed',
  ],
  async ({ objects: { license } }) => {
    await syncLicenseFromWebhook(license);
    console.log('License updated/activated/deactivated:', license);
  }
);
```

## Process Webhook Requests[â€‹](#process-webhook-requests "Direct link to Process Webhook Requests")

Once the listener is set up and handlers are registered, you need to process incoming webhook requests. The SDK provides two methods for this: `processFetch` for environments that support the Fetch API (like Next.js or Cloudflare Workers), and `processNodeHttp` for Node.js HTTP servers.

#### Using the Fetch API (e.g., Next.js, Cloudflare Workers)[â€‹](#using-the-fetch-api-eg-nextjs-cloudflare-workers "Direct link to Using the Fetch API (e.g., Next.js, Cloudflare Workers)")

The `processFetch` method processes requests in environments that support the Fetch API:

```
export async function POST(request: Request): Promise<Response> {
  return await webhookService.processFetch(listener, request);
}
```

You can use it in Next.js API routes, serverless functions, or any environment that supports the Fetch API.

Easy Next.js Integration

In Next.js, you can reduce boilerplate by using the `createRequestProcessor` method:

```
const processor = freemius.webhook.createRequestProcessor(listener);

export { processor as POST };
```

For a more framework-specific example, see the [full Next.js integration guide](https://freemius.com/help/help/documentation/saas-sdk/framework/nextjs/.md).

#### Using Node.js HTTP[â€‹](#using-nodejs-http "Direct link to Using Node.js HTTP")

The `processNodeHttp` method processes requests in a Node.js HTTP server:

```
import { createServer } from 'http';

const server = createServer(async (req, res) => {
  if (req.url === '/webhook') {
    await webhookService.processNodeHttp(listener, req, res);
  } else {
    res.statusCode = 404;
    res.end('Not Found');
  }
});

server.listen(3000, () => {
  console.log('Server running on http://localhost:3000');
});
```

## Security Considerations[â€‹](#security-considerations "Direct link to Security Considerations")

* The SDK automatically verifies the authenticity of incoming webhook requests using the secret key configured in the `Freemius` instance.
* If you want to create your own custom processor, make sure to use the `listener.verifyRequest` method to validate the request before processing it.