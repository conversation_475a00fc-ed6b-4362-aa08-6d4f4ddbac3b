// This is your Prisma schema file for MySQL,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
  output   = "../src/generated/prisma"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model User {
  id         String   @id @default(cuid())
  email      String   @unique
  password   String
  first_name String
  last_name  String
  role       UserRole
  is_active  <PERSON>olean  @default(true)
  created_at DateTime @default(now())
  updated_at DateTime @updatedAt

  // Enhanced security fields
  password_changed_at      DateTime?
  requires_password_change <PERSON><PERSON><PERSON>  @default(false)
  failed_login_attempts    Int      @default(0)
  locked_until            DateTime?
  last_login_ip           String?
  two_factor_secret       String?
  two_factor_enabled      Boolean  @default(false)

  // Profile settings
  profile_settings UserProfileSettings?

  // Relations
  admin_activities AdminActivity[]
  manual_tasks     ManualTask[]
  system_settings_created SystemSettings[]
  system_settings_updated SystemSettings[] @relation("SystemSettingsUpdatedBy")
  settings_changes        SettingsChangeLog[]
  sessions               UserSession[]
  password_history       PasswordHistory[]
  csrf_tokens           CsrfToken[]

  @@map("users")
}

model UserProfileSettings {
  id                    String   @id @default(cuid())
  user_id              String   @unique
  user                 User     @relation(fields: [user_id], references: [id], onDelete: Cascade)

  // Display preferences
  display_name         String?
  avatar_url           String?
  timezone             String   @default("UTC")
  date_format          String   @default("YYYY-MM-DD")
  time_format          String   @default("24h") // "12h" or "24h"
  language             String   @default("en")
  theme                String   @default("light") // "light", "dark", "auto"

  // Notification preferences
  email_notifications  Boolean  @default(true)
  security_alerts      Boolean  @default(true)
  system_updates       Boolean  @default(true)
  activity_digest      String   @default("weekly") // "never", "daily", "weekly", "monthly"

  // Privacy settings
  show_activity        Boolean  @default(false) // Show activity to other admins
  session_timeout      Int      @default(480) // Minutes (8 hours default)

  created_at           DateTime @default(now())
  updated_at           DateTime @updatedAt

  @@map("user_profile_settings")
}

model SystemSettings {
  id                   String   @id @default(cuid())
  key                  String   @unique
  value                Json
  category             String   // "security", "api", "notifications", "maintenance"
  description          String?
  data_type            String   // "string", "number", "boolean", "json", "array"
  is_public            Boolean  @default(false) // Can non-super-admins see this?
  requires_restart     Boolean  @default(false) // Does changing this require system restart?
  validation_rules     Json?    // JSON schema for validation

  created_by           String
  created_by_user      User     @relation(fields: [created_by], references: [id])
  updated_by           String?
  updated_by_user      User?    @relation("SystemSettingsUpdatedBy", fields: [updated_by], references: [id])

  created_at           DateTime @default(now())
  updated_at           DateTime @updatedAt

  @@map("system_settings")
}

model SettingsChangeLog {
  id                   String   @id @default(cuid())
  setting_key          String
  old_value            Json?
  new_value            Json
  changed_by           String
  changed_by_user      User     @relation(fields: [changed_by], references: [id])
  change_reason        String?
  ip_address           String?
  user_agent           String?

  created_at           DateTime @default(now())

  @@map("settings_change_log")
}

model UserSession {
  id                   String   @id @default(cuid())
  user_id              String
  user                 User     @relation(fields: [user_id], references: [id], onDelete: Cascade)

  session_token        String   @unique
  refresh_token        String?  @unique
  ip_address           String
  user_agent           String?
  location             String?  // Derived from IP
  device_fingerprint   String?

  is_active            Boolean  @default(true)
  last_activity        DateTime @default(now())
  expires_at           DateTime

  // Enhanced security fields
  security_flags       Json?    // Additional security metadata
  max_idle_time        Int      @default(28800)  // 8 hours in seconds
  absolute_timeout     Int      @default(86400)  // 24 hours in seconds

  created_at           DateTime @default(now())

  @@map("user_sessions")
}

model AdminActivity {
  id          String            @id @default(cuid())
  user_id     String
  action      AdminActionType
  resource    String // e.g., "user", "task", "system"
  resource_id String? // ID of the affected resource
  details     Json? // Additional context about the action
  ip_address  String?
  user_agent  String?
  created_at  DateTime          @default(now())

  // Relations
  user User @relation(fields: [user_id], references: [id])

  @@map("admin_activities")
}

model ManualTask {
  id          String           @id @default(cuid())
  title       String
  description String?
  status      TaskStatus       @default(PENDING)
  priority    TaskPriority     @default(MEDIUM)
  assigned_to String
  created_by  String
  due_date    DateTime?
  completed_at DateTime?
  created_at  DateTime         @default(now())
  updated_at  DateTime         @updatedAt

  // Relations
  user User @relation(fields: [assigned_to], references: [id])

  @@map("manual_tasks")
}

// New security-focused tables

model AccountLockout {
  id               String   @id @default(cuid())
  email            String   @unique
  failed_attempts  Int      @default(0)
  locked_until     DateTime?
  last_attempt_ip  String
  created_at       DateTime @default(now())
  updated_at       DateTime @updatedAt

  @@map("account_lockouts")
}

model PasswordHistory {
  id            String   @id @default(cuid())
  user_id       String
  user          User     @relation(fields: [user_id], references: [id], onDelete: Cascade)
  password_hash String
  created_at    DateTime @default(now())

  @@map("password_history")
}

model SecurityAuditLog {
  id            String   @id @default(cuid())
  event_type    String
  user_id       String?
  email         String?
  ip_address    String
  user_agent    String?
  resource      String?
  action        String?
  details       Json?
  success       Boolean
  error_message String?
  created_at    DateTime @default(now())

  @@map("security_audit_log")
}

model RateLimitTracking {
  id             String   @id @default(cuid())
  key_identifier String
  request_count  Int      @default(1)
  window_start   DateTime
  expires_at     DateTime
  created_at     DateTime @default(now())

  @@unique([key_identifier, window_start])
  @@map("rate_limit_tracking")
}

model CsrfToken {
  id         String   @id @default(cuid())
  token      String   @unique
  user_id    String
  user       User     @relation(fields: [user_id], references: [id], onDelete: Cascade)
  expires_at DateTime
  created_at DateTime @default(now())

  @@map("csrf_tokens")
}

enum UserRole {
  SUPER_ADMIN
  DEV
  MARKETING
  SALES
}

enum AdminActionType {
  CREATE_USER
  UPDATE_USER
  DELETE_USER
  ASSIGN_TASK
  COMPLETE_TASK
  UPDATE_SETTINGS
  LOGIN
  LOGOUT
  PASSWORD_RESET
  BULK_OPERATION
}

enum TaskStatus {
  PENDING
  IN_PROGRESS
  COMPLETED
  CANCELLED
}

enum TaskPriority {
  LOW
  MEDIUM
  HIGH
  URGENT
}