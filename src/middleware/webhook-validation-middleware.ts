import { Request, Response, NextFunction } from 'express';
import { FreemiusService } from '../services/freemius-service';
import { SecurityLogger, SecurityEventType } from '../lib/security-logger';
import { getClientIp } from './rate-limit-middleware';

// Extend Express Request interface to include raw body for signature validation
declare global {
    namespace Express {
        interface Request {
            rawBody?: Buffer;
        }
    }
}

/**
 * Middleware to validate Freemius webhook HMAC signatures
 * This middleware should run before any business logic to ensure security
 */
export function freemiusWebhookValidationMiddleware() {
    return async (req: Request, res: Response, next: NextFunction) => {
        try {
            const clientIp = getClientIp(req);
            const userAgent = req.headers['user-agent'] || '';
            const signature = req.headers['x-signature'] as string;

            // Log webhook attempt
            await SecurityLogger.logSecurityEvent({
                eventType: SecurityEventType.FREEMIUS_WEBHOOK_RECEIVED,
                ipAddress: clientIp,
                userAgent,
                resource: req.path,
                action: req.method,
                success: false, // Will be updated to true if validation passes
                details: {
                    hasSignature: !!signature,
                    contentType: req.headers['content-type'],
                    contentLength: req.headers['content-length'],
                    endpoint: req.path
                }
            });

            // Check if signature header is present
            if (!signature) {
                await SecurityLogger.logSecurityEvent({
                    eventType: SecurityEventType.FREEMIUS_WEBHOOK_FAILED,
                    ipAddress: clientIp,
                    userAgent,
                    resource: req.path,
                    action: req.method,
                    success: false,
                    details: {
                        reason: 'missing_signature_header',
                        endpoint: req.path
                    }
                });

                return res.status(403).json({
                    success: false,
                    error: 'Webhook signature validation failed',
                    message: 'Missing signature header'
                });
            }

            // Get raw body for signature validation
            let rawBody: string;
            if (req.rawBody) {
                rawBody = req.rawBody.toString('utf8');
            } else if (Buffer.isBuffer(req.body)) {
                // Handle raw buffer from express.raw() middleware
                rawBody = req.body.toString('utf8');
                // Parse the JSON for further processing
                try {
                    req.body = JSON.parse(rawBody);
                } catch (parseError) {
                    console.error('Failed to parse webhook JSON:', parseError);
                    return res.status(400).json({
                        success: false,
                        error: 'Invalid JSON payload',
                        message: 'Webhook payload must be valid JSON'
                    });
                }
            } else if (typeof req.body === 'string') {
                rawBody = req.body;
            } else {
                rawBody = JSON.stringify(req.body);
            }

            // Initialize Freemius service and validate signature
            const freemiusService = new FreemiusService();
            const validationResult = freemiusService.validateWebhookSignature(rawBody, signature);

            if (!validationResult.isValid) {
                await SecurityLogger.logSecurityEvent({
                    eventType: SecurityEventType.FREEMIUS_WEBHOOK_FAILED,
                    ipAddress: clientIp,
                    userAgent,
                    resource: req.path,
                    action: req.method,
                    success: false,
                    details: {
                        reason: 'invalid_signature',
                        error: validationResult.error,
                        endpoint: req.path,
                        signatureProvided: !!signature
                    }
                });

                return res.status(403).json({
                    success: false,
                    error: 'Webhook signature validation failed',
                    message: validationResult.error || 'Invalid signature'
                });
            }

            // Log successful validation
            await SecurityLogger.logSecurityEvent({
                eventType: SecurityEventType.FREEMIUS_WEBHOOK_RECEIVED,
                ipAddress: clientIp,
                userAgent,
                resource: req.path,
                action: req.method,
                success: true,
                details: {
                    endpoint: req.path,
                    signatureValid: true
                }
            });

            // Signature is valid, proceed to next middleware
            next();

        } catch (error) {
            const clientIp = getClientIp(req);
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';

            await SecurityLogger.logSecurityEvent({
                eventType: SecurityEventType.SECURITY_ALERT_TRIGGERED,
                ipAddress: clientIp,
                userAgent: req.headers['user-agent'] || '',
                resource: req.path,
                action: req.method,
                success: false,
                errorMessage,
                details: {
                    component: 'webhook_validation_middleware',
                    error: errorMessage,
                    endpoint: req.path
                }
            });

            console.error('Webhook validation middleware error:', error);

            return res.status(500).json({
                success: false,
                error: 'Internal server error during webhook validation',
                message: 'Webhook validation failed due to server error'
            });
        }
    };
}

/**
 * Middleware to capture raw body for webhook signature validation
 * This should be applied before the standard JSON body parser for webhook endpoints
 */
export function captureRawBodyMiddleware() {
    return (req: Request, _res: Response, next: NextFunction) => {
        const chunks: Buffer[] = [];

        req.on('data', (chunk: Buffer) => {
            chunks.push(chunk);
        });

        req.on('end', () => {
            req.rawBody = Buffer.concat(chunks);
            next();
        });

        req.on('error', (error) => {
            console.error('Error capturing raw body:', error);
            next(error);
        });
    };
}

/**
 * Combined middleware for webhook validation that includes both raw body capture and HMAC validation
 */
export function webhookValidationMiddleware() {
    return [
        captureRawBodyMiddleware(),
        freemiusWebhookValidationMiddleware()
    ];
}
