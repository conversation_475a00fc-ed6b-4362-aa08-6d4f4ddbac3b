// Authentication middleware
export {
    authMiddleware,
    auth,
    hasRole,
    hasRoleOr<PERSON>igher,
    createAuthMiddleware,
    sessionValidationMiddleware,
    type AuthMiddlewareOptions
} from './auth-middleware';

// Authorization middleware
export {
    authorizationMiddleware,
    authorize,
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    getRolePermissions,
    authorizeResource,
    requirePermissions,
    requireRoles,
    customAuthorization,
    requireOwnership,
    UserRole,
    Permission,
    type AuthorizationOptions,
    type ResourcePermission
} from './authorization-middleware';

// Rate limiting middleware
export {
    createRateLimitMiddleware,
    rateLimitMiddleware,
    createCustomRateLimit,
    getClientIp,
    type RateLimitMiddlewareOptions
} from './rate-limit-middleware';

// Security headers middleware
export {
    securityHeadersMiddleware,
    securityHeaders,
    getSecurityHeadersForEnvironment,
    type SecurityHeadersOptions
} from './security-headers-middleware';

// Input validation middleware
export {
    inputValidationMiddleware,
    validationSchemas,
    combineSchemas,
    validate,
    type ValidationRule,
    type ValidationSchema,
    type ValidationError
} from './input-validation-middleware';

// CSRF protection middleware
export {
    csrfProtection,
    csrfTokenProvider,
    csrf,
    validateCSRFToken,
    generateCSRFTokenForUser,
    type CSRFOptions
} from './csrf-middleware';

// Route protection middleware
export {
    applyRouteProtection,
    protectRoute,
    hierarchicalPermissionCheck,
    validateResourceOwnership,
    logPermissionDenial,
    protect,
    routeProtections,
    type RouteProtectionConfig
} from './route-protection';

// Webhook validation middleware
export {
    freemiusWebhookValidationMiddleware,
    captureRawBodyMiddleware,
    webhookValidationMiddleware
} from './webhook-validation-middleware';

// Combined security middleware stack
// Note: Import security-stack directly when needed due to module resolution issues
// export {
//   createSecurityMiddlewareStack,
//   securityStacks,
//   routeSecurity,
//   applySecurityMiddleware,
//   createRouteSecurityMiddleware,
//   methodSecurity,
//   getSecurityConfigForEnvironment,
//   type SecurityStackOptions
// } from './security-stack';