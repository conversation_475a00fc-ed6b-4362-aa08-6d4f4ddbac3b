import { Request, Response, NextFunction } from 'express';
import { SecurityLogger, SecurityEventType } from '../lib/security-logger';
import { getClientIp } from './rate-limit-middleware';

export interface ValidationRule {
    field: string;
    required?: boolean;
    type?: 'string' | 'number' | 'boolean' | 'email' | 'url' | 'uuid' | 'date';
    minLength?: number;
    maxLength?: number;
    min?: number;
    max?: number;
    pattern?: RegExp;
    enum?: string[];
    custom?: (value: any) => boolean | string;
    sanitize?: boolean;
}

export interface ValidationSchema {
    body?: ValidationRule[];
    query?: ValidationRule[];
    params?: ValidationRule[];
}

export interface ValidationError {
    field: string;
    message: string;
    value?: any;
}

/**
 * Sanitize string input to prevent XSS and injection attacks
 */
function sanitizeString(input: string): string {
    if (typeof input !== 'string') return input;

    return input
        // Remove null bytes
        .replace(/\0/g, '')
        // Remove control characters except newlines and tabs
        .replace(/[\x01-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '')
        // Basic HTML entity encoding for common XSS vectors
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;')
        .replace(/"/g, '&quot;')
        .replace(/'/g, '&#x27;')
        .replace(/\//g, '&#x2F;')
        // Remove potential SQL injection patterns
        .replace(/(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\b)/gi, '')
        // Trim whitespace
        .trim();
}

/**
 * Validate email format
 */
function isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email) && email.length <= 254;
}

/**
 * Validate URL format
 */
function isValidUrl(url: string): boolean {
    try {
        new URL(url);
        return true;
    } catch {
        return false;
    }
}

/**
 * Validate UUID format
 */
function isValidUuid(uuid: string): boolean {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return uuidRegex.test(uuid);
}

/**
 * Validate date format
 */
function isValidDate(date: string): boolean {
    const parsedDate = new Date(date);
    return !isNaN(parsedDate.getTime());
}

/**
 * Validate a single field against a rule
 */
function validateField(value: any, rule: ValidationRule): ValidationError | null {
    const { field, required, type, minLength, maxLength, min, max, pattern, enum: enumValues, custom } = rule;

    // Check if field is required
    if (required && (value === undefined || value === null || value === '')) {
        return { field, message: `${field} is required` };
    }

    // If field is not required and empty, skip validation
    if (!required && (value === undefined || value === null || value === '')) {
        return null;
    }

    // Type validation
    if (type) {
        switch (type) {
            case 'string':
                if (typeof value !== 'string') {
                    return { field, message: `${field} must be a string`, value };
                }
                break;
            case 'number':
                if (typeof value !== 'number' && !(!isNaN(Number(value)))) {
                    return { field, message: `${field} must be a number`, value };
                }
                value = Number(value);
                break;
            case 'boolean':
                if (typeof value !== 'boolean' && value !== 'true' && value !== 'false') {
                    return { field, message: `${field} must be a boolean`, value };
                }
                break;
            case 'email':
                if (typeof value !== 'string' || !isValidEmail(value)) {
                    return { field, message: `${field} must be a valid email address`, value };
                }
                break;
            case 'url':
                if (typeof value !== 'string' || !isValidUrl(value)) {
                    return { field, message: `${field} must be a valid URL`, value };
                }
                break;
            case 'uuid':
                if (typeof value !== 'string' || !isValidUuid(value)) {
                    return { field, message: `${field} must be a valid UUID`, value };
                }
                break;
            case 'date':
                if (typeof value !== 'string' || !isValidDate(value)) {
                    return { field, message: `${field} must be a valid date`, value };
                }
                break;
        }
    }

    // String length validation
    if (typeof value === 'string') {
        if (minLength !== undefined && value.length < minLength) {
            return { field, message: `${field} must be at least ${minLength} characters long`, value };
        }
        if (maxLength !== undefined && value.length > maxLength) {
            return { field, message: `${field} must be no more than ${maxLength} characters long`, value };
        }
    }

    // Number range validation
    if (typeof value === 'number') {
        if (min !== undefined && value < min) {
            return { field, message: `${field} must be at least ${min}`, value };
        }
        if (max !== undefined && value > max) {
            return { field, message: `${field} must be no more than ${max}`, value };
        }
    }

    // Pattern validation
    if (pattern && typeof value === 'string' && !pattern.test(value)) {
        return { field, message: `${field} format is invalid`, value };
    }

    // Enum validation
    if (enumValues && !enumValues.includes(value)) {
        return { field, message: `${field} must be one of: ${enumValues.join(', ')}`, value };
    }

    // Custom validation
    if (custom) {
        const customResult = custom(value);
        if (customResult !== true) {
            const message = typeof customResult === 'string' ? customResult : `${field} is invalid`;
            return { field, message, value };
        }
    }

    return null;
}

/**
 * Validate and sanitize request data
 */
function validateAndSanitize(data: any, rules: ValidationRule[]): {
    isValid: boolean;
    errors: ValidationError[];
    sanitizedData: any
} {
    const errors: ValidationError[] = [];
    const sanitizedData: any = {};

    // Validate each rule
    for (const rule of rules) {
        const value = data[rule.field];
        const error = validateField(value, rule);

        if (error) {
            errors.push(error);
        } else if (value !== undefined && value !== null) {
            // Sanitize the value if validation passed
            let sanitizedValue = value;

            if (rule.sanitize !== false && typeof value === 'string') {
                sanitizedValue = sanitizeString(value);
            }

            sanitizedData[rule.field] = sanitizedValue;
        }
    }

    // Check for unexpected fields (potential injection attempt)
    const allowedFields = rules.map(rule => rule.field);
    const unexpectedFields = Object.keys(data).filter(key => !allowedFields.includes(key));

    if (unexpectedFields.length > 0) {
        errors.push({
            field: 'unexpected_fields',
            message: `Unexpected fields: ${unexpectedFields.join(', ')}`
        });
    }

    return {
        isValid: errors.length === 0,
        errors,
        sanitizedData
    };
}

/**
 * Input validation middleware
 */
export function inputValidationMiddleware(schema: ValidationSchema) {
    return async (req: Request, res: Response, next: NextFunction) => {
        try {
            const allErrors: ValidationError[] = [];
            const clientIp = getClientIp(req);

            // Validate body
            if (schema.body && req.body) {
                const { isValid, errors, sanitizedData } = validateAndSanitize(req.body, schema.body);
                if (!isValid) {
                    allErrors.push(...errors);
                } else {
                    req.body = sanitizedData;
                }
            }

            // Validate query parameters
            if (schema.query && req.query) {
                const { isValid, errors, sanitizedData } = validateAndSanitize(req.query, schema.query);
                if (!isValid) {
                    allErrors.push(...errors);
                } else {
                    req.query = sanitizedData;
                }
            }

            // Validate URL parameters
            if (schema.params && req.params) {
                const { isValid, errors, sanitizedData } = validateAndSanitize(req.params, schema.params);
                if (!isValid) {
                    allErrors.push(...errors);
                } else {
                    req.params = sanitizedData;
                }
            }

            // If validation failed, log and return error
            if (allErrors.length > 0) {
                await SecurityLogger.logSecurityViolation({
                    eventType: SecurityEventType.INPUT_VALIDATION_FAILED,
                    ipAddress: clientIp,
                    userAgent: req.headers['user-agent'],
                    resource: req.path,
                    action: req.method,
                    success: false,
                    details: {
                        violationType: 'input_validation',
                        endpoint: req.path,
                        validationErrors: allErrors.map(e => `${e.field}: ${e.message}`)
                    }
                });

                return res.status(400).json({
                    error: 'Validation Error',
                    message: 'Invalid input data',
                    details: allErrors
                });
            }

            next();
        } catch (error) {
            console.error('Input validation middleware error:', error);

            // Log the error
            await SecurityLogger.logSecurityEvent({
                eventType: SecurityEventType.SECURITY_ALERT_TRIGGERED,
                ipAddress: getClientIp(req),
                userAgent: req.headers['user-agent'],
                resource: req.path,
                action: req.method,
                success: false,
                errorMessage: error instanceof Error ? error.message : 'Unknown validation error',
                details: {
                    component: 'input_validation_middleware',
                    error: error instanceof Error ? error.message : 'Unknown error'
                }
            });

            return res.status(500).json({
                error: 'Internal Server Error',
                message: 'Validation processing failed'
            });
        }
    };
}

/**
 * Common validation schemas
 */
export const validationSchemas = {
    // User authentication
    login: {
        body: [
            { field: 'email', required: true, type: 'email' as const, maxLength: 254 },
            { field: 'password', required: true, type: 'string' as const, minLength: 1, maxLength: 128 }
        ]
    },

    // User creation
    createUser: {
        body: [
            { field: 'email', required: true, type: 'email' as const, maxLength: 254 },
            { field: 'password', required: true, type: 'string' as const, minLength: 12, maxLength: 128 },
            { field: 'first_name', required: true, type: 'string' as const, minLength: 1, maxLength: 50 },
            { field: 'last_name', required: true, type: 'string' as const, minLength: 1, maxLength: 50 },
            { field: 'role', required: true, type: 'string' as const, enum: ['SUPER_ADMIN', 'DEV', 'MARKETING', 'SALES'] }
        ]
    },

    // Password change
    changePassword: {
        body: [
            { field: 'currentPassword', required: true, type: 'string' as const, minLength: 1, maxLength: 128 },
            { field: 'newPassword', required: true, type: 'string' as const, minLength: 12, maxLength: 128 }
        ]
    },

    // Password reset request
    passwordResetRequest: {
        body: [
            { field: 'email', required: true, type: 'email' as const, maxLength: 254 }
        ]
    },

    // Password reset
    passwordReset: {
        body: [
            { field: 'token', required: true, type: 'string' as const, minLength: 32, maxLength: 128 },
            { field: 'newPassword', required: true, type: 'string' as const, minLength: 12, maxLength: 128 }
        ]
    },

    // User ID parameter
    userIdParam: {
        params: [
            { field: 'id', required: true, type: 'uuid' as const }
        ]
    },

    // Pagination query
    pagination: {
        query: [
            { field: 'page', required: false, type: 'number' as const, min: 1, max: 1000 },
            { field: 'limit', required: false, type: 'number' as const, min: 1, max: 100 },
            { field: 'sort', required: false, type: 'string' as const, maxLength: 50 },
            { field: 'order', required: false, type: 'string' as const, enum: ['asc', 'desc'] }
        ]
    },

    // Search query
    search: {
        query: [
            { field: 'q', required: false, type: 'string' as const, minLength: 1, maxLength: 100 },
            { field: 'filter', required: false, type: 'string' as const, maxLength: 50 }
        ]
    },

    // Admin user creation
    createAdminUser: {
        body: [
            { field: 'email', required: true, type: 'email' as const, maxLength: 254 },
            { field: 'firstName', required: true, type: 'string' as const, minLength: 1, maxLength: 50 },
            { field: 'lastName', required: true, type: 'string' as const, minLength: 1, maxLength: 50 },
            { field: 'role', required: true, type: 'string' as const, enum: ['DEV', 'MARKETING', 'SALES'] },
            { field: 'temporaryPassword', required: false, type: 'string' as const, minLength: 12, maxLength: 128 },
            { field: 'requirePasswordChange', required: false, type: 'boolean' as const }
        ]
    },

    // Session ID parameter
    sessionIdParam: {
        params: [
            { field: 'sessionId', required: true, type: 'string' as const, minLength: 8, maxLength: 20 }
        ]
    },

    // IP Intelligence validation schemas
    ipLookup: {
        body: [
            {
                field: 'ip', required: true, type: 'string' as const,
                pattern: /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$|^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$|^::1$|^::$/,
                maxLength: 45
            }
        ]
    },

    ipRefresh: {
        params: [
            {
                field: 'ip', required: true, type: 'string' as const,
                pattern: /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$|^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$|^::1$|^::$/,
                maxLength: 45
            }
        ]
    },

    ipAnalysisHistory: {
        query: [
            { field: 'page', required: false, type: 'number' as const, min: 1, max: 1000 },
            { field: 'limit', required: false, type: 'number' as const, min: 1, max: 100 },
            { field: 'ip', required: false, type: 'string' as const, maxLength: 45 },
            { field: 'source', required: false, type: 'string' as const, enum: ['plugin', 'admin', 'webhook'] },
            { field: 'recommendation', required: false, type: 'string' as const, enum: ['allow', 'block', 'review'] },
            { field: 'startDate', required: false, type: 'date' as const },
            { field: 'endDate', required: false, type: 'date' as const }
        ]
    },

    // Freemius validation schemas
    freemiusProductList: {
        query: [
            { field: 'page', required: false, type: 'number' as const, min: 1, max: 1000 },
            { field: 'limit', required: false, type: 'number' as const, min: 1, max: 100 },
            { field: 'status', required: false, type: 'string' as const, enum: ['active', 'error', 'pending'] }
        ]
    },

    freemiusInstallationList: {
        query: [
            { field: 'page', required: false, type: 'number' as const, min: 1, max: 1000 },
            { field: 'limit', required: false, type: 'number' as const, min: 1, max: 100 },
            { field: 'productId', required: false, type: 'string' as const, maxLength: 50 },
            { field: 'status', required: false, type: 'string' as const, enum: ['active', 'inactive', 'premium', 'trial'] },
            { field: 'country', required: false, type: 'string' as const, maxLength: 2 }
        ]
    },

    freemiusEventHistory: {
        query: [
            { field: 'page', required: false, type: 'number' as const, min: 1, max: 1000 },
            { field: 'limit', required: false, type: 'number' as const, min: 1, max: 100 },
            { field: 'type', required: false, type: 'string' as const, maxLength: 50 },
            { field: 'productId', required: false, type: 'string' as const, maxLength: 50 },
            { field: 'status', required: false, type: 'string' as const, enum: ['pending', 'processed', 'error'] },
            { field: 'startDate', required: false, type: 'date' as const },
            { field: 'endDate', required: false, type: 'date' as const }
        ]
    }
};

/**
 * Combine multiple validation schemas
 */
export function combineSchemas(...schemas: ValidationSchema[]): ValidationSchema {
    const combined: ValidationSchema = {};

    for (const schema of schemas) {
        if (schema.body) {
            combined.body = [...(combined.body || []), ...schema.body];
        }
        if (schema.query) {
            combined.query = [...(combined.query || []), ...schema.query];
        }
        if (schema.params) {
            combined.params = [...(combined.params || []), ...schema.params];
        }
    }

    return combined;
}

/**
 * Create validation middleware with common patterns
 */
export const validate = {
    login: inputValidationMiddleware(validationSchemas.login),
    createUser: inputValidationMiddleware(validationSchemas.createUser),
    changePassword: inputValidationMiddleware(validationSchemas.changePassword),
    passwordResetRequest: inputValidationMiddleware(validationSchemas.passwordResetRequest),
    passwordReset: inputValidationMiddleware(validationSchemas.passwordReset),
    userIdParam: inputValidationMiddleware(validationSchemas.userIdParam),
    pagination: inputValidationMiddleware(validationSchemas.pagination),
    search: inputValidationMiddleware(validationSchemas.search),

    createAdminUser: inputValidationMiddleware(validationSchemas.createAdminUser),
    sessionIdParam: inputValidationMiddleware(validationSchemas.sessionIdParam),

    // Combined validations
    userWithPagination: inputValidationMiddleware(
        combineSchemas(validationSchemas.userIdParam, validationSchemas.pagination)
    ),
    searchWithPagination: inputValidationMiddleware(
        combineSchemas(validationSchemas.search, validationSchemas.pagination)
    ),

    // IP Intelligence validations
    ipLookup: inputValidationMiddleware(validationSchemas.ipLookup),
    ipRefresh: inputValidationMiddleware(validationSchemas.ipRefresh),
    ipAnalysisHistory: inputValidationMiddleware(validationSchemas.ipAnalysisHistory),

    // Freemius validations
    freemiusProductList: inputValidationMiddleware(validationSchemas.freemiusProductList),
    freemiusInstallationList: inputValidationMiddleware(validationSchemas.freemiusInstallationList),
    freemiusEventHistory: inputValidationMiddleware(validationSchemas.freemiusEventHistory)
};