import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { api } from '../services/api';
import { formatDate, getRoleDisplayName, getRoleBadgeColor } from '../utils/formatters';
import { validatePasswordChange } from '../utils/validation';
import {
  UserIcon,
  KeyIcon,
  ShieldCheckIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  EyeIcon,
  EyeSlashIcon,
  CogIcon,
  DevicePhoneMobileIcon,
  ComputerDesktopIcon,
  GlobeAltIcon,
  ClockIcon,
  BellIcon,
  PaintBrushIcon,
  LanguageIcon,
} from '@heroicons/react/24/outline';

interface UserProfile {
  id: string;
  user: {
    id: string;
    email: string;
    role: string;
    is_2fa_enabled: boolean;
    created_at: string;
    updated_at: string;
    last_login: string | null;
  };
  display_name: string | null;
  avatar_url: string | null;
  timezone: string;
  date_format: string;
  time_format: string;
  language: string;
  theme: string;
  email_notifications: boolean;
  security_alerts: boolean;
  system_updates: boolean;
  activity_digest: string;
  show_activity: boolean;
  session_timeout: number;
  created_at: string;
  updated_at: string;
}

interface UserSession {
  id: string;
  ip_address: string;
  user_agent: string | null;
  location: string | null;
  device_fingerprint: string | null;
  is_active: boolean;
  last_activity: string;
  expires_at: string;
  created_at: string;
}

interface ProfileSettingsFormProps {
  profile: UserProfile;
  onSuccess: () => void;
}

function ProfileSettingsForm({ profile, onSuccess }: ProfileSettingsFormProps) {
  const [formData, setFormData] = useState({
    display_name: profile.display_name || '',
    timezone: profile.timezone,
    date_format: profile.date_format,
    time_format: profile.time_format,
    language: profile.language,
    theme: profile.theme,
    email_notifications: profile.email_notifications,
    security_alerts: profile.security_alerts,
    system_updates: profile.system_updates,
    activity_digest: profile.activity_digest,
    show_activity: profile.show_activity,
    session_timeout: profile.session_timeout,
  });
  const [errors, setErrors] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const timezones = [
    'UTC', 'America/New_York', 'America/Chicago', 'America/Denver', 'America/Los_Angeles',
    'Europe/London', 'Europe/Paris', 'Europe/Berlin', 'Europe/Rome', 'Europe/Madrid',
    'Asia/Tokyo', 'Asia/Shanghai', 'Asia/Kolkata', 'Asia/Dubai', 'Australia/Sydney'
  ];

  const languages = [
    { code: 'en', name: 'English' },
    { code: 'es', name: 'Español' },
    { code: 'fr', name: 'Français' },
    { code: 'de', name: 'Deutsch' },
    { code: 'it', name: 'Italiano' },
    { code: 'pt', name: 'Português' },
    { code: 'ru', name: 'Русский' },
    { code: 'zh', name: '中文' },
    { code: 'ja', name: '日本語' },
    { code: 'ko', name: '한국어' }
  ];

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    setIsLoading(true);
    setErrors([]);

    try {
      const response = await api.updateProfile(formData);

      if (response.success) {
        onSuccess();
      } else {
        setErrors([response.error || 'Failed to update profile']);
      }
    } catch (error: any) {
      setErrors([error.message || 'An unexpected error occurred']);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="bg-white shadow rounded-lg">
      <div className="px-4 py-5 sm:p-6">
        <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4 flex items-center">
          <CogIcon className="h-5 w-5 mr-2" />
          Profile Settings
        </h3>

        <form onSubmit={handleSubmit} className="space-y-6">
          {errors.length > 0 && (
            <div className="bg-red-50 border border-red-200 rounded-md p-4">
              <div className="flex">
                <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-red-800">
                    {errors.length === 1 ? 'Error' : 'Errors'}
                  </h3>
                  <div className="mt-2 text-sm text-red-700">
                    <ul className="list-disc pl-5 space-y-1">
                      {errors.map((error, index) => (
                        <li key={index}>{error}</li>
                      ))}
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Display Settings */}
          <div className="space-y-4">
            <h4 className="text-md font-medium text-gray-900 flex items-center">
              <PaintBrushIcon className="h-4 w-4 mr-2" />
              Display Settings
            </h4>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label htmlFor="display_name" className="block text-sm font-medium text-gray-700">
                  Display Name
                </label>
                <input
                  type="text"
                  id="display_name"
                  value={formData.display_name}
                  onChange={(e) => setFormData({ ...formData, display_name: e.target.value })}
                  className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                  placeholder="Your display name"
                />
              </div>

              <div>
                <label htmlFor="theme" className="block text-sm font-medium text-gray-700">
                  Theme
                </label>
                <select
                  id="theme"
                  value={formData.theme}
                  onChange={(e) => setFormData({ ...formData, theme: e.target.value })}
                  className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                >
                  <option value="light">Light</option>
                  <option value="dark">Dark</option>
                  <option value="auto">Auto</option>
                </select>
              </div>

              <div>
                <label htmlFor="language" className="block text-sm font-medium text-gray-700">
                  <LanguageIcon className="h-4 w-4 inline mr-1" />
                  Language
                </label>
                <select
                  id="language"
                  value={formData.language}
                  onChange={(e) => setFormData({ ...formData, language: e.target.value })}
                  className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                >
                  {languages.map((lang) => (
                    <option key={lang.code} value={lang.code}>
                      {lang.name}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label htmlFor="timezone" className="block text-sm font-medium text-gray-700">
                  <GlobeAltIcon className="h-4 w-4 inline mr-1" />
                  Timezone
                </label>
                <select
                  id="timezone"
                  value={formData.timezone}
                  onChange={(e) => setFormData({ ...formData, timezone: e.target.value })}
                  className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                >
                  {timezones.map((tz) => (
                    <option key={tz} value={tz}>
                      {tz}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label htmlFor="date_format" className="block text-sm font-medium text-gray-700">
                  Date Format
                </label>
                <select
                  id="date_format"
                  value={formData.date_format}
                  onChange={(e) => setFormData({ ...formData, date_format: e.target.value })}
                  className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                >
                  <option value="YYYY-MM-DD">YYYY-MM-DD</option>
                  <option value="MM/DD/YYYY">MM/DD/YYYY</option>
                  <option value="DD/MM/YYYY">DD/MM/YYYY</option>
                  <option value="DD-MM-YYYY">DD-MM-YYYY</option>
                </select>
              </div>

              <div>
                <label htmlFor="time_format" className="block text-sm font-medium text-gray-700">
                  Time Format
                </label>
                <select
                  id="time_format"
                  value={formData.time_format}
                  onChange={(e) => setFormData({ ...formData, time_format: e.target.value })}
                  className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                >
                  <option value="24h">24 Hour</option>
                  <option value="12h">12 Hour</option>
                </select>
              </div>
            </div>
          </div>

          {/* Notification Settings */}
          <div className="space-y-4">
            <h4 className="text-md font-medium text-gray-900 flex items-center">
              <BellIcon className="h-4 w-4 mr-2" />
              Notification Settings
            </h4>

            <div className="space-y-3">
              <div className="flex items-center">
                <input
                  id="email_notifications"
                  type="checkbox"
                  checked={formData.email_notifications}
                  onChange={(e) => setFormData({ ...formData, email_notifications: e.target.checked })}
                  className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                />
                <label htmlFor="email_notifications" className="ml-2 block text-sm text-gray-900">
                  Email notifications
                </label>
              </div>

              <div className="flex items-center">
                <input
                  id="security_alerts"
                  type="checkbox"
                  checked={formData.security_alerts}
                  onChange={(e) => setFormData({ ...formData, security_alerts: e.target.checked })}
                  className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                />
                <label htmlFor="security_alerts" className="ml-2 block text-sm text-gray-900">
                  Security alerts
                </label>
              </div>

              <div className="flex items-center">
                <input
                  id="system_updates"
                  type="checkbox"
                  checked={formData.system_updates}
                  onChange={(e) => setFormData({ ...formData, system_updates: e.target.checked })}
                  className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                />
                <label htmlFor="system_updates" className="ml-2 block text-sm text-gray-900">
                  System updates
                </label>
              </div>

              <div>
                <label htmlFor="activity_digest" className="block text-sm font-medium text-gray-700">
                  Activity Digest
                </label>
                <select
                  id="activity_digest"
                  value={formData.activity_digest}
                  onChange={(e) => setFormData({ ...formData, activity_digest: e.target.value })}
                  className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                >
                  <option value="never">Never</option>
                  <option value="daily">Daily</option>
                  <option value="weekly">Weekly</option>
                  <option value="monthly">Monthly</option>
                </select>
              </div>
            </div>
          </div>

          {/* Privacy & Security Settings */}
          <div className="space-y-4">
            <h4 className="text-md font-medium text-gray-900 flex items-center">
              <ShieldCheckIcon className="h-4 w-4 mr-2" />
              Privacy & Security
            </h4>

            <div className="space-y-3">
              <div className="flex items-center">
                <input
                  id="show_activity"
                  type="checkbox"
                  checked={formData.show_activity}
                  onChange={(e) => setFormData({ ...formData, show_activity: e.target.checked })}
                  className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                />
                <label htmlFor="show_activity" className="ml-2 block text-sm text-gray-900">
                  Show my activity to other admins
                </label>
              </div>

              <div>
                <label htmlFor="session_timeout" className="block text-sm font-medium text-gray-700">
                  <ClockIcon className="h-4 w-4 inline mr-1" />
                  Session Timeout (minutes)
                </label>
                <input
                  type="number"
                  id="session_timeout"
                  min="30"
                  max="1440"
                  value={formData.session_timeout}
                  onChange={(e) => setFormData({ ...formData, session_timeout: parseInt(e.target.value) })}
                  className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                />
                <p className="mt-1 text-sm text-gray-500">
                  Between 30 minutes and 24 hours (1440 minutes)
                </p>
              </div>
            </div>
          </div>

          <div className="flex justify-end">
            <button
              type="submit"
              disabled={isLoading}
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
            >
              {isLoading ? 'Saving...' : 'Save Settings'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}

interface PasswordChangeFormProps {
  onSuccess: () => void;
}

function PasswordChangeForm({ onSuccess }: PasswordChangeFormProps) {
  const [formData, setFormData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
  });
  const [errors, setErrors] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showPasswords, setShowPasswords] = useState({
    current: false,
    new: false,
    confirm: false,
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const validationErrors = validatePasswordChange(
      formData.currentPassword,
      formData.newPassword,
      formData.confirmPassword
    );

    if (validationErrors.length > 0) {
      setErrors(validationErrors);
      return;
    }

    setIsLoading(true);
    setErrors([]);

    try {
      const response = await api.changePassword({
        current_password: formData.currentPassword,
        new_password: formData.newPassword,
      });

      if (response.success) {
        setFormData({
          currentPassword: '',
          newPassword: '',
          confirmPassword: '',
        });
        onSuccess();
      } else {
        setErrors([response.error || 'Failed to change password']);
      }
    } catch (error: any) {
      setErrors([error.message || 'An unexpected error occurred']);
    } finally {
      setIsLoading(false);
    }
  };

  const togglePasswordVisibility = (field: 'current' | 'new' | 'confirm') => {
    setShowPasswords(prev => ({
      ...prev,
      [field]: !prev[field],
    }));
  };

  return (
    <div className="bg-white shadow rounded-lg">
      <div className="px-4 py-5 sm:p-6">
        <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4 flex items-center">
          <KeyIcon className="h-5 w-5 mr-2" />
          Change Password
        </h3>

        <form onSubmit={handleSubmit} className="space-y-4">
          {errors.length > 0 && (
            <div className="bg-red-50 border border-red-200 rounded-md p-4">
              <div className="flex">
                <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-red-800">
                    {errors.length === 1 ? 'Error' : 'Errors'}
                  </h3>
                  <div className="mt-2 text-sm text-red-700">
                    <ul className="list-disc pl-5 space-y-1">
                      {errors.map((error, index) => (
                        <li key={index}>{error}</li>
                      ))}
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          )}

          <div>
            <label htmlFor="currentPassword" className="block text-sm font-medium text-gray-700">
              Current Password
            </label>
            <div className="mt-1 relative">
              <input
                type={showPasswords.current ? 'text' : 'password'}
                id="currentPassword"
                required
                value={formData.currentPassword}
                onChange={(e) => setFormData({ ...formData, currentPassword: e.target.value })}
                className="block w-full pr-10 border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              />
              <button
                type="button"
                className="absolute inset-y-0 right-0 pr-3 flex items-center"
                onClick={() => togglePasswordVisibility('current')}
              >
                {showPasswords.current ? (
                  <EyeSlashIcon className="h-4 w-4 text-gray-400" />
                ) : (
                  <EyeIcon className="h-4 w-4 text-gray-400" />
                )}
              </button>
            </div>
          </div>

          <div>
            <label htmlFor="newPassword" className="block text-sm font-medium text-gray-700">
              New Password
            </label>
            <div className="mt-1 relative">
              <input
                type={showPasswords.new ? 'text' : 'password'}
                id="newPassword"
                required
                value={formData.newPassword}
                onChange={(e) => setFormData({ ...formData, newPassword: e.target.value })}
                className="block w-full pr-10 border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                placeholder="Minimum 12 characters"
              />
              <button
                type="button"
                className="absolute inset-y-0 right-0 pr-3 flex items-center"
                onClick={() => togglePasswordVisibility('new')}
              >
                {showPasswords.new ? (
                  <EyeSlashIcon className="h-4 w-4 text-gray-400" />
                ) : (
                  <EyeIcon className="h-4 w-4 text-gray-400" />
                )}
              </button>
            </div>
          </div>

          <div>
            <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700">
              Confirm New Password
            </label>
            <div className="mt-1 relative">
              <input
                type={showPasswords.confirm ? 'text' : 'password'}
                id="confirmPassword"
                required
                value={formData.confirmPassword}
                onChange={(e) => setFormData({ ...formData, confirmPassword: e.target.value })}
                className="block w-full pr-10 border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              />
              <button
                type="button"
                className="absolute inset-y-0 right-0 pr-3 flex items-center"
                onClick={() => togglePasswordVisibility('confirm')}
              >
                {showPasswords.confirm ? (
                  <EyeSlashIcon className="h-4 w-4 text-gray-400" />
                ) : (
                  <EyeIcon className="h-4 w-4 text-gray-400" />
                )}
              </button>
            </div>
          </div>

          <div className="flex justify-end">
            <button
              type="submit"
              disabled={isLoading}
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
            >
              {isLoading ? 'Changing...' : 'Change Password'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}

interface SessionManagementProps {
  sessions: UserSession[];
  onRevokeSession: (sessionId: string) => void;
  onRevokeAllOther: () => void;
  isLoading: boolean;
}

function SessionManagement({ sessions, onRevokeSession, onRevokeAllOther, isLoading }: SessionManagementProps) {
  const getDeviceIcon = (userAgent: string | null) => {
    if (!userAgent) return ComputerDesktopIcon;

    if (userAgent.includes('Mobile') || userAgent.includes('Android') || userAgent.includes('iPhone')) {
      return DevicePhoneMobileIcon;
    }

    return ComputerDesktopIcon;
  };

  const getDeviceInfo = (userAgent: string | null) => {
    if (!userAgent) return 'Unknown Device';

    if (userAgent.includes('Chrome')) return 'Chrome Browser';
    if (userAgent.includes('Firefox')) return 'Firefox Browser';
    if (userAgent.includes('Safari')) return 'Safari Browser';
    if (userAgent.includes('Edge')) return 'Edge Browser';

    return 'Unknown Browser';
  };

  return (
    <div className="bg-white shadow rounded-lg">
      <div className="px-4 py-5 sm:p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg leading-6 font-medium text-gray-900 flex items-center">
            <DevicePhoneMobileIcon className="h-5 w-5 mr-2" />
            Active Sessions
          </h3>
          {sessions.length > 1 && (
            <button
              onClick={onRevokeAllOther}
              disabled={isLoading}
              className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
            >
              Revoke All Others
            </button>
          )}
        </div>

        <div className="space-y-4">
          {sessions.map((session) => {
            const DeviceIcon = getDeviceIcon(session.user_agent);
            const isCurrentSession = session.id === 'current'; // This would be determined by the backend

            return (
              <div key={session.id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                <div className="flex items-center space-x-4">
                  <div className="flex-shrink-0">
                    <DeviceIcon className="h-8 w-8 text-gray-400" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-900">
                      {getDeviceInfo(session.user_agent)}
                      {isCurrentSession && (
                        <span className="ml-2 inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                          Current
                        </span>
                      )}
                    </p>
                    <p className="text-sm text-gray-500">
                      {session.ip_address} • {session.location || 'Unknown Location'}
                    </p>
                    <p className="text-sm text-gray-500">
                      Last active: {formatDate(session.last_activity)}
                    </p>
                  </div>
                </div>

                {!isCurrentSession && (
                  <button
                    onClick={() => onRevokeSession(session.id)}
                    disabled={isLoading}
                    className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50"
                  >
                    Revoke
                  </button>
                )}
              </div>
            );
          })}

          {sessions.length === 0 && (
            <p className="text-sm text-gray-500 text-center py-4">
              No active sessions found.
            </p>
          )}
        </div>
      </div>
    </div>
  );
}

interface TwoFactorManagementProps {
  user: any;
  onUpdate: () => void;
}

function TwoFactorManagement({ user, onUpdate }: TwoFactorManagementProps) {
  const [isEnabling, setIsEnabling] = useState(false);
  const [isDisabling, setIsDisabling] = useState(false);
  const [qrCode, setQrCode] = useState<string | null>(null);
  const [secret, setSecret] = useState<string | null>(null);
  const [verificationToken, setVerificationToken] = useState('');
  const [disableToken, setDisableToken] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [showDisableForm, setShowDisableForm] = useState(false);

  const handleEnable2FA = async () => {
    setIsEnabling(true);
    setError(null);

    try {
      const response = await api.enable2FA();

      if (response.success && response.data) {
        setQrCode(response.data.qr_code);
        setSecret(response.data.secret);
      } else {
        setError(response.error || 'Failed to enable 2FA');
      }
    } catch (error: any) {
      setError(error.message || 'An unexpected error occurred');
    } finally {
      setIsEnabling(false);
    }
  };

  const handleDisable2FA = async () => {
    if (!disableToken.trim()) {
      setError('Please enter your 2FA token');
      return;
    }

    setIsDisabling(true);
    setError(null);

    try {
      const response = await api.disable2FA(disableToken);

      if (response.success) {
        setShowDisableForm(false);
        setDisableToken('');
        onUpdate();
      } else {
        setError(response.error || 'Failed to disable 2FA');
      }
    } catch (error: any) {
      setError(error.message || 'An unexpected error occurred');
    } finally {
      setIsDisabling(false);
    }
  };

  const handleVerify2FA = async () => {
    if (!verificationToken.trim()) {
      setError('Please enter your 2FA token');
      return;
    }

    if (verificationToken.length !== 6) {
      setError('2FA token must be 6 digits');
      return;
    }

    try {
      setIsLoading(true);
      setError('');

      const response = await fetch('/api/auth/2fa/verify', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          token: verificationToken,
          secret: secret
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to verify 2FA token');
      }

      if (data.success) {
        setQrCode(null);
        setSecret(null);
        setVerificationToken('');
        setError('');
        onUpdate(); // Refresh the profile data
      } else {
        throw new Error(data.error || 'Invalid 2FA token');
      }
    } catch (error: any) {
      setError(error.message || 'An unexpected error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="bg-white shadow rounded-lg">
      <div className="px-4 py-5 sm:p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg leading-6 font-medium text-gray-900">
            Two-Factor Authentication
          </h3>
          <div className="flex items-center">
            {user.is_2fa_enabled ? (
              <>
                <ShieldCheckIcon className="h-5 w-5 text-green-500 mr-2" />
                <span className="text-sm font-medium text-green-700">Enabled</span>
              </>
            ) : (
              <>
                <KeyIcon className="h-5 w-5 text-yellow-500 mr-2" />
                <span className="text-sm font-medium text-yellow-700">Disabled</span>
              </>
            )}
          </div>
        </div>

        {error && (
          <div className="mb-4 bg-red-50 border border-red-200 rounded-md p-4">
            <div className="flex">
              <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">Error</h3>
                <div className="mt-2 text-sm text-red-700">{error}</div>
              </div>
            </div>
          </div>
        )}

        {!user.is_2fa_enabled ? (
          <div>
            {!qrCode ? (
              <div>
                <p className="text-sm text-gray-600 mb-4">
                  Two-factor authentication adds an extra layer of security to your account.
                  You'll need an authenticator app like Google Authenticator or Authy.
                </p>
                <button
                  onClick={handleEnable2FA}
                  disabled={isEnabling}
                  className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
                >
                  {isEnabling ? 'Setting up...' : 'Enable 2FA'}
                </button>
              </div>
            ) : (
              <div>
                <p className="text-sm text-gray-600 mb-4">
                  Scan this QR code with your authenticator app, then enter the 6-digit code to complete setup.
                </p>
                <div className="mb-4">
                  <img src={qrCode} alt="2FA QR Code" className="mx-auto" />
                </div>
                <div className="mb-4">
                  <p className="text-sm text-gray-600 mb-2">Or enter this secret manually:</p>
                  <code className="bg-gray-100 px-2 py-1 rounded text-sm">{secret}</code>
                </div>
                <div className="flex space-x-3">
                  <input
                    type="text"
                    value={verificationToken}
                    onChange={(e) => setVerificationToken(e.target.value.replace(/\D/g, ''))}
                    maxLength={6}
                    className="block w-32 border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm text-center"
                    placeholder="000000"
                  />
                  <button
                    onClick={handleVerify2FA}
                    disabled={verificationToken.length !== 6}
                    className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50"
                  >
                    Verify & Enable
                  </button>
                </div>
              </div>
            )}
          </div>
        ) : (
          <div>
            {!showDisableForm ? (
              <div>
                <p className="text-sm text-gray-600 mb-4">
                  Two-factor authentication is currently enabled for your account.
                </p>
                <button
                  onClick={() => setShowDisableForm(true)}
                  className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                >
                  Disable 2FA
                </button>
              </div>
            ) : (
              <div>
                <p className="text-sm text-gray-600 mb-4">
                  Enter your current 2FA code to disable two-factor authentication.
                </p>
                <div className="flex space-x-3">
                  <input
                    type="text"
                    value={disableToken}
                    onChange={(e) => setDisableToken(e.target.value.replace(/\D/g, ''))}
                    maxLength={6}
                    className="block w-32 border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm text-center"
                    placeholder="000000"
                  />
                  <button
                    onClick={handleDisable2FA}
                    disabled={isDisabling || disableToken.length !== 6}
                    className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50"
                  >
                    {isDisabling ? 'Disabling...' : 'Disable 2FA'}
                  </button>
                  <button
                    onClick={() => {
                      setShowDisableForm(false);
                      setDisableToken('');
                      setError(null);
                    }}
                    className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                  >
                    Cancel
                  </button>
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}

export default function UserProfilePage() {
  const { user, refreshAuth } = useAuth();
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [sessions, setSessions] = useState<UserSession[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [isSessionLoading, setIsSessionLoading] = useState(false);

  useEffect(() => {
    loadProfile();
    loadSessions();
  }, []);

  const loadProfile = async () => {
    try {
      const response = await api.getProfile();
      if (response.success && response.data) {
        setProfile(response.data);
      }
    } catch (error) {
      console.error('Failed to load profile:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const loadSessions = async () => {
    try {
      const response = await api.getUserSessions();
      if (response.success && response.data) {
        setSessions(response.data);
      }
    } catch (error) {
      console.error('Failed to load sessions:', error);
    }
  };

  const handleProfileUpdateSuccess = () => {
    setSuccessMessage('Profile updated successfully');
    setTimeout(() => setSuccessMessage(null), 5000);
    loadProfile();
  };

  const handlePasswordChangeSuccess = () => {
    setSuccessMessage('Password changed successfully');
    setTimeout(() => setSuccessMessage(null), 5000);
  };

  const handle2FAUpdate = () => {
    refreshAuth();
    setSuccessMessage('2FA settings updated successfully');
    setTimeout(() => setSuccessMessage(null), 5000);
  };

  const handleRevokeSession = async (sessionId: string) => {
    setIsSessionLoading(true);
    try {
      const response = await api.revokeSession(sessionId);
      if (response.success) {
        setSessions(prev => prev.filter(s => s.id !== sessionId));
        setSuccessMessage('Session revoked successfully');
        setTimeout(() => setSuccessMessage(null), 5000);
      }
    } catch (error) {
      console.error('Failed to revoke session:', error);
    } finally {
      setIsSessionLoading(false);
    }
  };

  const handleRevokeAllOtherSessions = async () => {
    setIsSessionLoading(true);
    try {
      const response = await api.revokeAllOtherSessions();
      if (response.success) {
        loadSessions(); // Reload to get current state
        setSuccessMessage(`${response.data?.count || 0} sessions revoked successfully`);
        setTimeout(() => setSuccessMessage(null), 5000);
      }
    } catch (error) {
      console.error('Failed to revoke sessions:', error);
    } finally {
      setIsSessionLoading(false);
    }
  };

  if (isLoading || !profile) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">User Profile</h1>
        <p className="mt-1 text-sm text-gray-600">
          Manage your account settings, security preferences, and active sessions
        </p>
      </div>

      {/* Success Message */}
      {successMessage && (
        <div className="bg-green-50 border border-green-200 rounded-md p-4">
          <div className="flex">
            <CheckCircleIcon className="h-5 w-5 text-green-400" />
            <div className="ml-3">
              <p className="text-sm font-medium text-green-800">{successMessage}</p>
            </div>
          </div>
        </div>
      )}

      {/* Profile Information */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4 flex items-center">
            <UserIcon className="h-5 w-5 mr-2" />
            Profile Information
          </h3>

          <div className="flex items-center mb-6">
            <div className="flex-shrink-0 h-16 w-16">
              {profile.avatar_url ? (
                <img
                  className="h-16 w-16 rounded-full object-cover"
                  src={profile.avatar_url}
                  alt="Profile"
                />
              ) : (
                <div className="h-16 w-16 rounded-full bg-gray-300 flex items-center justify-center">
                  <UserIcon className="h-8 w-8 text-gray-600" />
                </div>
              )}
            </div>
            <div className="ml-6">
              <h4 className="text-lg font-medium text-gray-900">
                {profile.display_name || profile.user.email}
              </h4>
              <p className="text-sm text-gray-500">{profile.user.email}</p>
              <div className="flex items-center mt-1">
                <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getRoleBadgeColor(profile.user.role)}`}>
                  {getRoleDisplayName(profile.user.role)}
                </span>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <dt className="text-sm font-medium text-gray-500">User ID</dt>
              <dd className="mt-1 text-sm text-gray-900 font-mono">{profile.user.id}</dd>
            </div>
            <div>
              <dt className="text-sm font-medium text-gray-500">Account Created</dt>
              <dd className="mt-1 text-sm text-gray-900">{formatDate(profile.user.created_at)}</dd>
            </div>
            <div>
              <dt className="text-sm font-medium text-gray-500">Last Updated</dt>
              <dd className="mt-1 text-sm text-gray-900">{formatDate(profile.updated_at)}</dd>
            </div>
            <div>
              <dt className="text-sm font-medium text-gray-500">Last Login</dt>
              <dd className="mt-1 text-sm text-gray-900">
                {profile.user.last_login ? formatDate(profile.user.last_login) : 'Never'}
              </dd>
            </div>
            <div>
              <dt className="text-sm font-medium text-gray-500">Timezone</dt>
              <dd className="mt-1 text-sm text-gray-900">{profile.timezone}</dd>
            </div>
            <div>
              <dt className="text-sm font-medium text-gray-500">Language</dt>
              <dd className="mt-1 text-sm text-gray-900">{profile.language.toUpperCase()}</dd>
            </div>
          </div>
        </div>
      </div>

      {/* Profile Settings */}
      <ProfileSettingsForm profile={profile} onSuccess={handleProfileUpdateSuccess} />

      {/* Password Change */}
      <PasswordChangeForm onSuccess={handlePasswordChangeSuccess} />

      {/* Two-Factor Authentication */}
      <TwoFactorManagement user={profile.user} onUpdate={handle2FAUpdate} />

      {/* Session Management */}
      <SessionManagement
        sessions={sessions}
        onRevokeSession={handleRevokeSession}
        onRevokeAllOther={handleRevokeAllOtherSessions}
        isLoading={isSessionLoading}
      />
    </div>
  );
}