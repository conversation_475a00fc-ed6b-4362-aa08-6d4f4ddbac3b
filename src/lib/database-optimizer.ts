import { prisma } from './prisma';
import { Logger } from './logger';

export interface OptimizationResult {
    operation: string;
    success: boolean;
    message: string;
    details: {
        beforeSize?: number;
        afterSize?: number;
        timeTaken: number;
        recordsAffected?: number;
    };
}

export interface DatabaseStats {
    totalSize: number;
    tableStats: {
        tableName: string;
        recordCount: number;
        estimatedSize: number;
    }[];
    indexStats: {
        indexName: string;
        tableName: string;
        isUnique: boolean;
    }[];
}

export class DatabaseOptimizer {
    /**
     * Run comprehensive database optimization
     */
    async optimizeDatabase(): Promise<OptimizationResult[]> {
        const results: OptimizationResult[] = [];

        // 1. Vacuum database
        results.push(await this.vacuumDatabase());

        // 2. Analyze statistics
        results.push(await this.analyzeStatistics());

        // 3. Reindex database
        results.push(await this.reindexDatabase());

        // 4. Clean up fragmentation
        results.push(await this.defragmentTables());

        return results;
    }

    /**
     * Vacuum the database to reclaim space
     */
    async vacuumDatabase(): Promise<OptimizationResult> {
        const startTime = Date.now();

        try {
            // Get database size before vacuum
            const beforeStats = await this.getDatabaseStats();

            // Run VACUUM command
            await prisma.$executeRaw`VACUUM`;

            // Get database size after vacuum
            const afterStats = await this.getDatabaseStats();

            return {
                operation: 'vacuum',
                success: true,
                message: 'Database vacuum completed successfully',
                details: {
                    beforeSize: beforeStats.totalSize,
                    afterSize: afterStats.totalSize,
                    timeTaken: Date.now() - startTime
                }
            };

        } catch (error) {
            return {
                operation: 'vacuum',
                success: false,
                message: `Database vacuum failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
                details: {
                    timeTaken: Date.now() - startTime
                }
            };
        }
    }

    /**
     * Analyze database statistics for query optimization
     */
    async analyzeStatistics(): Promise<OptimizationResult> {
        const startTime = Date.now();

        try {
            // Run ANALYZE command to update statistics
            await prisma.$executeRaw`ANALYZE`;

            return {
                operation: 'analyze',
                success: true,
                message: 'Database statistics analysis completed successfully',
                details: {
                    timeTaken: Date.now() - startTime
                }
            };

        } catch (error) {
            return {
                operation: 'analyze',
                success: false,
                message: `Database analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
                details: {
                    timeTaken: Date.now() - startTime
                }
            };
        }
    }

    /**
     * Reindex database for optimal performance
     */
    async reindexDatabase(): Promise<OptimizationResult> {
        const startTime = Date.now();

        try {
            // For SQLite, REINDEX rebuilds all indexes
            await prisma.$executeRaw`REINDEX`;

            return {
                operation: 'reindex',
                success: true,
                message: 'Database reindexing completed successfully',
                details: {
                    timeTaken: Date.now() - startTime
                }
            };

        } catch (error) {
            return {
                operation: 'reindex',
                success: false,
                message: `Database reindexing failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
                details: {
                    timeTaken: Date.now() - startTime
                }
            };
        }
    }

    /**
     * Defragment tables by rebuilding them
     */
    async defragmentTables(): Promise<OptimizationResult> {
        const startTime = Date.now();

        try {
            // For SQLite, VACUUM already handles defragmentation
            // This is a placeholder for other database systems

            return {
                operation: 'defragment',
                success: true,
                message: 'Table defragmentation completed (handled by VACUUM)',
                details: {
                    timeTaken: Date.now() - startTime
                }
            };

        } catch (error) {
            return {
                operation: 'defragment',
                success: false,
                message: `Table defragmentation failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
                details: {
                    timeTaken: Date.now() - startTime
                }
            };
        }
    }

    /**
     * Get comprehensive database statistics
     */
    async getDatabaseStats(): Promise<DatabaseStats> {
        try {
            // Get table statistics
            const tableStats = await this.getTableStatistics();

            // Get index statistics
            const indexStats = await this.getIndexStatistics();

            // Calculate total size (approximation for SQLite)
            const totalSize = tableStats.reduce((sum, table) => sum + table.estimatedSize, 0);

            return {
                totalSize,
                tableStats,
                indexStats
            };

        } catch (error) {
            return {
                totalSize: 0,
                tableStats: [],
                indexStats: []
            };
        }
    }

    /**
     * Get statistics for all tables
     */
    private async getTableStatistics(): Promise<DatabaseStats['tableStats']> {
        const tables = [
            'users',
            'user_profile_settings',
            'system_settings',
            'settings_change_log',
            'user_sessions',
            'admin_activities',
            'manual_tasks',
            'account_lockouts',
            'password_history',
            'security_audit_log',
            'rate_limit_tracking',
            'csrf_tokens',
            'password_reset_tokens',
            'freemius_products',
            'freemius_installations',
            'freemius_events',
            'ip_registry_data',
            'ip_analysis_requests'
        ];

        const tableStats = [];

        for (const tableName of tables) {
            try {
                // Get record count
                const countResult = await prisma.$queryRawUnsafe(`
          SELECT COUNT(*) as count FROM "${tableName}"
        `) as any[];

                const recordCount = countResult[0]?.count || 0;

                // Estimate size (rough approximation)
                const estimatedSize = recordCount * 1000; // Assume 1KB per record on average

                tableStats.push({
                    tableName,
                    recordCount: Number(recordCount),
                    estimatedSize
                });

            } catch (error) {
                // Skip tables that don't exist or can't be queried
                continue;
            }
        }

        return tableStats;
    }

    /**
     * Get index statistics
     */
    private async getIndexStatistics(): Promise<DatabaseStats['indexStats']> {
        try {
            // For SQLite, get index information from sqlite_master
            const indexInfo = await prisma.$queryRaw`
        SELECT name as indexName, tbl_name as tableName, sql
        FROM sqlite_master 
        WHERE type = 'index' AND name NOT LIKE 'sqlite_%'
        ORDER BY tbl_name, name
      ` as any[];

            return indexInfo.map((index: any) => ({
                indexName: index.indexName,
                tableName: index.tableName,
                isUnique: index.sql?.includes('UNIQUE') || false
            }));

        } catch (error) {
            return [];
        }
    }

    /**
     * Check database integrity
     */
    async checkIntegrity(): Promise<OptimizationResult> {
        const startTime = Date.now();

        try {
            // Run integrity check
            const integrityResult = await prisma.$queryRaw`PRAGMA integrity_check` as any[];

            const isHealthy = integrityResult.length === 1 && integrityResult[0].integrity_check === 'ok';

            return {
                operation: 'integrity_check',
                success: isHealthy,
                message: isHealthy ? 'Database integrity check passed' : 'Database integrity issues found',
                details: {
                    timeTaken: Date.now() - startTime,
                    recordsAffected: integrityResult.length
                }
            };

        } catch (error) {
            return {
                operation: 'integrity_check',
                success: false,
                message: `Database integrity check failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
                details: {
                    timeTaken: Date.now() - startTime
                }
            };
        }
    }

    /**
     * Get slow query analysis
     *
     * Note: SQLite doesn't have built-in slow query logging like MySQL/PostgreSQL.
     * To implement this properly, you would need to:
     * 1. Enable query logging at the application level
     * 2. Use a performance monitoring tool like Prisma's query engine logs
     * 3. Implement custom query timing middleware
     *
     * For production use, consider:
     * - Using Prisma's query logging: https://www.prisma.io/docs/concepts/components/prisma-client/working-with-prismaclient/logging
     * - Implementing custom query performance tracking
     * - Using database-specific tools when switching to PostgreSQL/MySQL
     */
    async analyzeSlowQueries(): Promise<{
        query: string;
        avgExecutionTime: number;
        executionCount: number;
        recommendations?: string[];
    }[]> {
        try {
            // For SQLite, we can analyze the query plan for common tables
            const commonQueries = [
                'SELECT * FROM users WHERE email = ?',
                'SELECT * FROM user_sessions WHERE user_id = ?',
                'SELECT * FROM security_audit_log WHERE user_id = ?',
                'SELECT * FROM freemius_installations WHERE is_active = ?'
            ];

            const analysis = [];

            for (const query of commonQueries) {
                try {
                    // Use EXPLAIN QUERY PLAN to analyze query performance
                    const plan = await prisma.$queryRawUnsafe(`EXPLAIN QUERY PLAN ${query.replace('?', "'placeholder'")}`);

                    // Basic analysis based on query plan
                    const hasIndex = Array.isArray(plan) && plan.some((row: any) =>
                        row.detail && row.detail.includes('USING INDEX')
                    );

                    analysis.push({
                        query: query.replace('?', '[parameter]'),
                        avgExecutionTime: hasIndex ? 1 : 10, // Estimated based on index usage
                        executionCount: 0, // Would need actual logging
                        recommendations: hasIndex ? [] : [
                            'Consider adding an index for better performance',
                            'Review WHERE clause for optimization opportunities'
                        ]
                    });
                } catch (error) {
                    // Skip queries that can't be analyzed
                    continue;
                }
            }

            return analysis;
        } catch (error) {
            Logger.error('Error analyzing slow queries:', error);
            return [];
        }
    }

    /**
     * Get database performance metrics
     *
     * Note: SQLite has limited performance metrics compared to server databases.
     * This function provides what's available and estimates for others.
     */
    async getPerformanceMetrics(): Promise<{
        cacheHitRatio: number;
        avgQueryTime: number;
        connectionCount: number;
        lockWaitTime: number;
        databaseSize: number;
        pageSize: number;
        pageCount: number;
        freePages: number;
        walMode: boolean;
    }> {
        try {
            // Get actual SQLite statistics where possible
            const [
                pageSizeResult,
                pageCountResult,
                freePagesResult,
                walModeResult,
                dbSizeResult
            ] = await Promise.all([
                prisma.$queryRaw`PRAGMA page_size` as any[],
                prisma.$queryRaw`PRAGMA page_count` as any[],
                prisma.$queryRaw`PRAGMA freelist_count` as any[],
                prisma.$queryRaw`PRAGMA journal_mode` as any[],
                prisma.$queryRaw`PRAGMA database_list` as any[]
            ]);

            const pageSize = pageSizeResult[0]?.page_size || 4096;
            const pageCount = pageCountResult[0]?.page_count || 0;
            const freePages = freePagesResult[0]?.freelist_count || 0;
            const walMode = walModeResult[0]?.journal_mode === 'wal';
            const databaseSize = pageSize * pageCount;

            // Calculate estimated cache hit ratio based on free pages
            const usedPages = pageCount - freePages;
            const estimatedCacheHitRatio = usedPages > 0 ? Math.min(0.95, (usedPages / pageCount) * 0.9 + 0.1) : 0.1;

            return {
                cacheHitRatio: estimatedCacheHitRatio,
                avgQueryTime: 0, // Would need query logging middleware
                connectionCount: 1, // SQLite is single-connection by design
                lockWaitTime: 0, // SQLite handles locking internally
                databaseSize,
                pageSize,
                pageCount,
                freePages,
                walMode
            };

        } catch (error) {
            return {
                cacheHitRatio: 0,
                avgQueryTime: 0,
                connectionCount: 0,
                lockWaitTime: 0
            };
        }
    }

    /**
     * Optimize specific table
     */
    async optimizeTable(tableName: string): Promise<OptimizationResult> {
        const startTime = Date.now();

        try {
            // For SQLite, we can't optimize individual tables, but we can analyze them
            await prisma.$executeRawUnsafe(`ANALYZE "${tableName}"`);

            return {
                operation: `optimize_table_${tableName}`,
                success: true,
                message: `Table ${tableName} optimization completed`,
                details: {
                    timeTaken: Date.now() - startTime
                }
            };

        } catch (error) {
            return {
                operation: `optimize_table_${tableName}`,
                success: false,
                message: `Table ${tableName} optimization failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
                details: {
                    timeTaken: Date.now() - startTime
                }
            };
        }
    }
}

export const databaseOptimizer = new DatabaseOptimizer();