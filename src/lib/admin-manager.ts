import prisma from './prisma';
import { hashPassword, validatePasswordComplexity, addPasswordToHistory } from './auth';
import { SecurityLogger, SecurityEventType } from './security-logger';
import crypto from 'crypto';

// Admin management interfaces
export interface CreateAdminRequest {
    email: string;
    firstName: string;
    lastName: string;
    role: 'DEV' | 'MARKETING' | 'SALES';
    temporaryPassword?: string;
    requirePasswordChange?: boolean;
}

export interface UpdateAdminRequest {
    email?: string;
    firstName?: string;
    lastName?: string;
    role?: 'DEV' | 'MARKETING' | 'SALES';
    isActive?: boolean;
}

export interface AdminCreationResult {
    success: boolean;
    user?: any;
    temporaryPassword?: string;
    error?: string;
}

export interface AdminValidationResult {
    isValid: boolean;
    errors: string[];
}

// Role hierarchy for permission validation
const ROLE_HIERARCHY = {
    SUPER_ADMIN: 4,
    DEV: 3,
    MARKETING: 2,
    SALES: 1
} as const;

type UserRole = keyof typeof ROLE_HIERARCHY;

/**
 * Validates if a creator has permission to create/manage a user with the target role
 */
export function validateHierarchicalPermissions(
    creatorRole: UserRole,
    targetRole: UserRole
): AdminValidationResult {
    const errors: string[] = [];

    // Only SUPER_ADMIN can create other admins
    if (creatorRole !== 'SUPER_ADMIN') {
        errors.push('Only Super Admins can create admin users');
        return { isValid: false, errors };
    }

    // SUPER_ADMIN can create DEV, MARKETING, and SALES, but not other SUPER_ADMINs
    if (targetRole === 'SUPER_ADMIN') {
        errors.push('Cannot create additional Super Admin users');
        return { isValid: false, errors };
    }

    // Validate target role is valid
    if (!['DEV', 'MARKETING', 'SALES'].includes(targetRole)) {
        errors.push('Invalid target role. Must be DEV, MARKETING, or SALES');
        return { isValid: false, errors };
    }

    return { isValid: true, errors: [] };
}

/**
 * Generates a secure temporary password meeting complexity requirements
 */
export function generateTemporaryPassword(): string {
    // Generate a password with guaranteed complexity
    const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    const lowercase = 'abcdefghijklmnopqrstuvwxyz';
    const numbers = '0123456789';
    const specialChars = '!@#$%^&*()_+-=[]{}|;:,.<>?';

    // Ensure at least one character from each category
    let password = '';
    password += uppercase[Math.floor(Math.random() * uppercase.length)];
    password += lowercase[Math.floor(Math.random() * lowercase.length)];
    password += numbers[Math.floor(Math.random() * numbers.length)];
    password += specialChars[Math.floor(Math.random() * specialChars.length)];

    // Fill remaining characters (minimum 12 total)
    const allChars = uppercase + lowercase + numbers + specialChars;
    for (let i = password.length; i < 16; i++) {
        password += allChars[Math.floor(Math.random() * allChars.length)];
    }

    // Shuffle the password to randomize character positions
    return password.split('').sort(() => Math.random() - 0.5).join('');
}

/**
 * Validates admin user creation request
 */
export function validateAdminCreationRequest(request: CreateAdminRequest): AdminValidationResult {
    const errors: string[] = [];

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(request.email)) {
        errors.push('Invalid email format');
    }

    // Validate names
    if (!request.firstName || request.firstName.trim().length < 1) {
        errors.push('First name is required');
    }

    if (!request.lastName || request.lastName.trim().length < 1) {
        errors.push('Last name is required');
    }

    // Validate role
    if (!['DEV', 'MARKETING', 'SALES'].includes(request.role)) {
        errors.push('Invalid role. Must be DEV, MARKETING, or SALES');
    }

    // Validate temporary password if provided
    if (request.temporaryPassword) {
        const passwordValidation = validatePasswordComplexity(request.temporaryPassword);
        if (!passwordValidation.isValid) {
            errors.push(`Temporary password validation failed: ${passwordValidation.errors.join(', ')}`);
        }
    }

    return { isValid: errors.length === 0, errors };
}

/**
 * Creates a new admin user with proper validation and security controls
 */
export async function createAdminUser(
    creatorId: string,
    adminData: CreateAdminRequest,
    ipAddress: string = 'unknown',
    userAgent?: string
): Promise<AdminCreationResult> {
    try {
        // Get creator information for permission validation
        const creator = await prisma.user.findUnique({
            where: { id: creatorId },
            select: { id: true, email: true, role: true, is_active: true }
        });

        if (!creator) {
            await SecurityLogger.logAdminAction({
                eventType: SecurityEventType.ADMIN_USER_CREATED,
                userId: creatorId,
                ipAddress,
                userAgent,
                resource: 'admin_user',
                action: 'creation_failed',
                success: false,
                errorMessage: 'Creator not found',
                details: {
                    reason: 'invalid_creator',
                    creatorId
                }
            });

            return {
                success: false,
                error: 'Invalid creator credentials'
            };
        }

        if (!creator.is_active) {
            await SecurityLogger.logAdminAction({
                eventType: SecurityEventType.ADMIN_USER_CREATED,
                userId: creatorId,
                email: creator.email,
                ipAddress,
                userAgent,
                resource: 'admin_user',
                action: 'creation_failed',
                success: false,
                errorMessage: 'Creator account is inactive',
                details: {
                    reason: 'creator_inactive',
                    creatorId,
                    creatorEmail: creator.email
                }
            });

            return {
                success: false,
                error: 'Access denied'
            };
        }

        // Validate hierarchical permissions
        const permissionValidation = validateHierarchicalPermissions(
            creator.role as UserRole,
            adminData.role as UserRole
        );

        if (!permissionValidation.isValid) {
            await SecurityLogger.logAdminAction({
                eventType: SecurityEventType.ADMIN_USER_CREATED,
                userId: creatorId,
                email: creator.email,
                ipAddress,
                userAgent,
                resource: 'admin_user',
                action: 'creation_failed',
                success: false,
                errorMessage: 'Insufficient permissions',
                details: {
                    reason: 'permission_denied',
                    creatorRole: creator.role,
                    targetRole: adminData.role,
                    validationErrors: permissionValidation.errors
                }
            });

            return {
                success: false,
                error: 'Insufficient permissions to create admin user'
            };
        }

        // Validate admin creation request
        const requestValidation = validateAdminCreationRequest(adminData);
        if (!requestValidation.isValid) {
            await SecurityLogger.logAdminAction({
                eventType: SecurityEventType.ADMIN_USER_CREATED,
                userId: creatorId,
                email: creator.email,
                ipAddress,
                userAgent,
                resource: 'admin_user',
                action: 'creation_failed',
                success: false,
                errorMessage: 'Invalid request data',
                details: {
                    reason: 'validation_failed',
                    validationErrors: requestValidation.errors,
                    targetEmail: adminData.email
                }
            });

            return {
                success: false,
                error: `Validation failed: ${requestValidation.errors.join(', ')}`
            };
        }

        // Check email uniqueness across all users
        const existingUser = await prisma.user.findUnique({
            where: { email: adminData.email.toLowerCase() }
        });

        if (existingUser) {
            await SecurityLogger.logAdminAction({
                eventType: SecurityEventType.ADMIN_USER_CREATED,
                userId: creatorId,
                email: creator.email,
                ipAddress,
                userAgent,
                resource: 'admin_user',
                action: 'creation_failed',
                success: false,
                errorMessage: 'Email already exists',
                details: {
                    reason: 'email_already_exists',
                    targetEmail: adminData.email,
                    existingUserId: existingUser.id,
                    existingUserRole: existingUser.role
                }
            });

            return {
                success: false,
                error: 'Email address is already in use'
            };
        }

        // Generate or use provided temporary password
        const temporaryPassword = adminData.temporaryPassword || generateTemporaryPassword();

        // Hash the password
        const hashedPassword = await hashPassword(temporaryPassword);

        // Create the admin user
        const newAdminUser = await prisma.user.create({
            data: {
                email: adminData.email.toLowerCase(),
                password: hashedPassword,
                first_name: adminData.firstName.trim(),
                last_name: adminData.lastName.trim(),
                role: adminData.role,
                is_active: true,
                password_changed_at: new Date(),
                requires_password_change: adminData.requirePasswordChange !== false, // Default to true
                failed_login_attempts: 0,
                created_at: new Date(),
                updated_at: new Date()
            }
        });

        // Add password to history
        await addPasswordToHistory(newAdminUser.id, hashedPassword);

        // Log successful admin creation
        await SecurityLogger.logAdminAction({
            eventType: SecurityEventType.ADMIN_USER_CREATED,
            userId: creatorId,
            email: creator.email,
            ipAddress,
            userAgent,
            resource: 'admin_user',
            action: 'creation_success',
            success: true,
            details: {
                targetUserId: newAdminUser.id,
                targetEmail: newAdminUser.email,
                targetRole: newAdminUser.role,
                creatorId,
                creatorEmail: creator.email,
                creatorRole: creator.role,
                requiresPasswordChange: newAdminUser.requires_password_change,
                createdAt: newAdminUser.created_at
            }
        });

        // Return user data without password
        const { password: _, ...userWithoutPassword } = newAdminUser;

        return {
            success: true,
            user: userWithoutPassword,
            temporaryPassword: adminData.temporaryPassword ? undefined : temporaryPassword
        };

    } catch (error) {
        console.error('Admin user creation error:', error);

        // Log system error
        await SecurityLogger.logAdminAction({
            eventType: SecurityEventType.ADMIN_USER_CREATED,
            userId: creatorId,
            ipAddress,
            userAgent,
            resource: 'admin_user',
            action: 'creation_error',
            success: false,
            errorMessage: error instanceof Error ? error.message : 'Unknown error during admin creation',
            details: {
                reason: 'system_error',
                targetEmail: adminData.email,
                errorType: error instanceof Error ? error.constructor.name : 'UnknownError'
            }
        });

        return {
            success: false,
            error: 'Failed to create admin user due to system error'
        };
    }
}

/**
 * Updates an existing admin user with proper validation and security controls
 */
export async function updateAdminUser(
    updaterId: string,
    targetUserId: string,
    updates: UpdateAdminRequest,
    ipAddress: string = 'unknown',
    userAgent?: string
): Promise<AdminCreationResult> {
    try {
        // Get updater information for permission validation
        const updater = await prisma.user.findUnique({
            where: { id: updaterId },
            select: { id: true, email: true, role: true, is_active: true }
        });

        if (!updater || !updater.is_active) {
            await SecurityLogger.logAdminAction({
                eventType: SecurityEventType.ADMIN_USER_UPDATED,
                userId: updaterId,
                ipAddress,
                userAgent,
                resource: 'admin_user',
                action: 'update_failed',
                success: false,
                errorMessage: 'Invalid updater credentials',
                details: {
                    reason: 'invalid_updater',
                    updaterId,
                    targetUserId
                }
            });

            return {
                success: false,
                error: 'Access denied'
            };
        }

        // Get target user information
        const targetUser = await prisma.user.findUnique({
            where: { id: targetUserId },
            select: { id: true, email: true, role: true, is_active: true, first_name: true, last_name: true }
        });

        if (!targetUser) {
            await SecurityLogger.logAdminAction({
                eventType: SecurityEventType.ADMIN_USER_UPDATED,
                userId: updaterId,
                email: updater.email,
                ipAddress,
                userAgent,
                resource: 'admin_user',
                action: 'update_failed',
                success: false,
                errorMessage: 'Target user not found',
                details: {
                    reason: 'target_not_found',
                    updaterId,
                    targetUserId
                }
            });

            return {
                success: false,
                error: 'User not found'
            };
        }

        // Validate permissions for role changes
        if (updates.role) {
            const permissionValidation = validateHierarchicalPermissions(
                updater.role as UserRole,
                updates.role as UserRole
            );

            if (!permissionValidation.isValid) {
                await SecurityLogger.logAdminAction({
                    eventType: SecurityEventType.ADMIN_USER_UPDATED,
                    userId: updaterId,
                    email: updater.email,
                    ipAddress,
                    userAgent,
                    resource: 'admin_user',
                    action: 'update_failed',
                    success: false,
                    errorMessage: 'Insufficient permissions for role change',
                    details: {
                        reason: 'permission_denied',
                        updaterRole: updater.role,
                        currentTargetRole: targetUser.role,
                        newTargetRole: updates.role,
                        validationErrors: permissionValidation.errors
                    }
                });

                return {
                    success: false,
                    error: 'Insufficient permissions to modify user role'
                };
            }

            // Prevent changing SUPER_ADMIN role
            if (targetUser.role === 'SUPER_ADMIN') {
                await SecurityLogger.logAdminAction({
                    eventType: SecurityEventType.ADMIN_USER_UPDATED,
                    userId: updaterId,
                    email: updater.email,
                    ipAddress,
                    userAgent,
                    resource: 'admin_user',
                    action: 'update_failed',
                    success: false,
                    errorMessage: 'Cannot modify Super Admin role',
                    details: {
                        reason: 'super_admin_protection',
                        targetUserId,
                        targetEmail: targetUser.email
                    }
                });

                return {
                    success: false,
                    error: 'Cannot modify Super Admin users'
                };
            }
        }

        // Check email uniqueness if email is being updated
        if (updates.email && updates.email !== targetUser.email) {
            const existingUser = await prisma.user.findUnique({
                where: { email: updates.email.toLowerCase() }
            });

            if (existingUser) {
                await SecurityLogger.logAdminAction({
                    eventType: SecurityEventType.ADMIN_USER_UPDATED,
                    userId: updaterId,
                    email: updater.email,
                    ipAddress,
                    userAgent,
                    resource: 'admin_user',
                    action: 'update_failed',
                    success: false,
                    errorMessage: 'Email already exists',
                    details: {
                        reason: 'email_already_exists',
                        targetUserId,
                        newEmail: updates.email,
                        existingUserId: existingUser.id
                    }
                });

                return {
                    success: false,
                    error: 'Email address is already in use'
                };
            }
        }

        // Prepare update data
        const updateData: any = {
            updated_at: new Date()
        };

        if (updates.email) updateData.email = updates.email.toLowerCase();
        if (updates.firstName) updateData.first_name = updates.firstName.trim();
        if (updates.lastName) updateData.last_name = updates.lastName.trim();
        if (updates.role) updateData.role = updates.role;
        if (updates.isActive !== undefined) updateData.is_active = updates.isActive;

        // Update the user
        const updatedUser = await prisma.user.update({
            where: { id: targetUserId },
            data: updateData
        });

        // Log successful update
        await SecurityLogger.logAdminAction({
            eventType: SecurityEventType.ADMIN_USER_UPDATED,
            userId: updaterId,
            email: updater.email,
            ipAddress,
            userAgent,
            resource: 'admin_user',
            action: 'update_success',
            success: true,
            details: {
                targetUserId,
                targetEmail: updatedUser.email,
                updaterId,
                updaterEmail: updater.email,
                updaterRole: updater.role,
                changes: updates,
                previousValues: {
                    email: targetUser.email,
                    firstName: targetUser.first_name,
                    lastName: targetUser.last_name,
                    role: targetUser.role,
                    isActive: targetUser.is_active
                }
            }
        });

        // Return updated user without password
        const { password: _, ...userWithoutPassword } = updatedUser;

        return {
            success: true,
            user: userWithoutPassword
        };

    } catch (error) {
        console.error('Admin user update error:', error);

        // Log system error
        await SecurityLogger.logAdminAction({
            eventType: SecurityEventType.ADMIN_USER_UPDATED,
            userId: updaterId,
            ipAddress,
            userAgent,
            resource: 'admin_user',
            action: 'update_error',
            success: false,
            errorMessage: error instanceof Error ? error.message : 'Unknown error during admin update',
            details: {
                reason: 'system_error',
                targetUserId,
                updates,
                errorType: error instanceof Error ? error.constructor.name : 'UnknownError'
            }
        });

        return {
            success: false,
            error: 'Failed to update admin user due to system error'
        };
    }
}

/**
 * Deactivates an admin user (soft delete)
 */
export async function deactivateAdminUser(
    deactivatorId: string,
    targetUserId: string,
    ipAddress: string = 'unknown',
    userAgent?: string
): Promise<AdminCreationResult> {
    try {
        // Get deactivator information
        const deactivator = await prisma.user.findUnique({
            where: { id: deactivatorId },
            select: { id: true, email: true, role: true, is_active: true }
        });

        if (!deactivator || !deactivator.is_active) {
            return {
                success: false,
                error: 'Access denied'
            };
        }

        // Get target user information
        const targetUser = await prisma.user.findUnique({
            where: { id: targetUserId },
            select: { id: true, email: true, role: true, is_active: true }
        });

        if (!targetUser) {
            return {
                success: false,
                error: 'User not found'
            };
        }

        // Prevent deactivating SUPER_ADMIN
        if (targetUser.role === 'SUPER_ADMIN') {
            await SecurityLogger.logAdminAction({
                eventType: SecurityEventType.ADMIN_USER_UPDATED,
                userId: deactivatorId,
                email: deactivator.email,
                ipAddress,
                userAgent,
                resource: 'admin_user',
                action: 'deactivation_failed',
                success: false,
                errorMessage: 'Cannot deactivate Super Admin',
                details: {
                    reason: 'super_admin_protection',
                    targetUserId,
                    targetEmail: targetUser.email
                }
            });

            return {
                success: false,
                error: 'Cannot deactivate Super Admin users'
            };
        }

        // Only SUPER_ADMIN can deactivate other admins
        if (deactivator.role !== 'SUPER_ADMIN') {
            await SecurityLogger.logAdminAction({
                eventType: SecurityEventType.ADMIN_USER_UPDATED,
                userId: deactivatorId,
                email: deactivator.email,
                ipAddress,
                userAgent,
                resource: 'admin_user',
                action: 'deactivation_failed',
                success: false,
                errorMessage: 'Insufficient permissions',
                details: {
                    reason: 'permission_denied',
                    deactivatorRole: deactivator.role,
                    targetUserId,
                    targetRole: targetUser.role
                }
            });

            return {
                success: false,
                error: 'Insufficient permissions to deactivate admin user'
            };
        }

        // Deactivate the user
        const deactivatedUser = await prisma.user.update({
            where: { id: targetUserId },
            data: {
                is_active: false,
                updated_at: new Date()
            }
        });

        // Invalidate all user sessions
        await prisma.userSession.updateMany({
            where: { user_id: targetUserId },
            data: { is_active: false }
        });

        // Log successful deactivation
        await SecurityLogger.logAdminAction({
            eventType: SecurityEventType.ADMIN_USER_UPDATED,
            userId: deactivatorId,
            email: deactivator.email,
            ipAddress,
            userAgent,
            resource: 'admin_user',
            action: 'deactivation_success',
            success: true,
            details: {
                targetUserId,
                targetEmail: deactivatedUser.email,
                targetRole: deactivatedUser.role,
                deactivatorId,
                deactivatorEmail: deactivator.email,
                sessionsInvalidated: true
            }
        });

        // Return deactivated user without password
        const { password: _, ...userWithoutPassword } = deactivatedUser;

        return {
            success: true,
            user: userWithoutPassword
        };

    } catch (error) {
        console.error('Admin user deactivation error:', error);

        // Log system error
        await SecurityLogger.logAdminAction({
            eventType: SecurityEventType.ADMIN_USER_UPDATED,
            userId: deactivatorId,
            ipAddress,
            userAgent,
            resource: 'admin_user',
            action: 'deactivation_error',
            success: false,
            errorMessage: error instanceof Error ? error.message : 'Unknown error during admin deactivation',
            details: {
                reason: 'system_error',
                targetUserId,
                errorType: error instanceof Error ? error.constructor.name : 'UnknownError'
            }
        });

        return {
            success: false,
            error: 'Failed to deactivate admin user due to system error'
        };
    }
}

/**
 * Forces a password change requirement for an admin user
 */
export async function requirePasswordChangeOnFirstLogin(
    userId: string,
    ipAddress: string = 'system',
    userAgent?: string
): Promise<void> {
    try {
        await prisma.user.update({
            where: { id: userId },
            data: {
                requires_password_change: true,
                updated_at: new Date()
            }
        });

        // Log password change requirement
        await SecurityLogger.logAdminAction({
            eventType: SecurityEventType.ADMIN_USER_UPDATED,
            userId: 'system',
            ipAddress,
            userAgent,
            resource: 'admin_user',
            action: 'password_change_required',
            success: true,
            details: {
                targetUserId: userId,
                reason: 'first_login_requirement'
            }
        });

    } catch (error) {
        console.error('Error setting password change requirement:', error);

        await SecurityLogger.logAdminAction({
            eventType: SecurityEventType.ADMIN_USER_UPDATED,
            userId: 'system',
            ipAddress,
            userAgent,
            resource: 'admin_user',
            action: 'password_change_requirement_error',
            success: false,
            errorMessage: error instanceof Error ? error.message : 'Unknown error',
            details: {
                targetUserId: userId,
                reason: 'system_error'
            }
        });

        throw error;
    }
}

/**
 * Gets admin users with filtering and pagination
 */
export async function getAdminUsers(
    requesterId: string,
    filters?: {
        role?: 'DEV' | 'MARKETING' | 'SALES';
        isActive?: boolean;
        search?: string;
    },
    pagination?: {
        page?: number;
        limit?: number;
    }
) {
    try {
        // Verify requester permissions
        const requester = await prisma.user.findUnique({
            where: { id: requesterId },
            select: { role: true, is_active: true }
        });

        if (!requester || !requester.is_active || requester.role !== 'SUPER_ADMIN') {
            throw new Error('Insufficient permissions to view admin users');
        }

        const page = pagination?.page || 1;
        const limit = pagination?.limit || 10;
        const skip = (page - 1) * limit;

        // Build where clause
        const where: any = {
            role: {
                in: ['DEV', 'MARKETING', 'SALES']
            }
        };

        if (filters?.role) {
            where.role = filters.role;
        }

        if (filters?.isActive !== undefined) {
            where.is_active = filters.isActive;
        }

        if (filters?.search) {
            where.OR = [
                { email: { contains: filters.search, mode: 'insensitive' } },
                { first_name: { contains: filters.search, mode: 'insensitive' } },
                { last_name: { contains: filters.search, mode: 'insensitive' } }
            ];
        }

        // Get users and total count
        const [users, total] = await Promise.all([
            prisma.user.findMany({
                where,
                select: {
                    id: true,
                    email: true,
                    first_name: true,
                    last_name: true,
                    role: true,
                    is_active: true,
                    requires_password_change: true,
                    last_login_ip: true,
                    created_at: true,
                    updated_at: true
                },
                skip,
                take: limit,
                orderBy: { created_at: 'desc' }
            }),
            prisma.user.count({ where })
        ]);

        return {
            users,
            pagination: {
                page,
                limit,
                total,
                totalPages: Math.ceil(total / limit)
            }
        };

    } catch (error) {
        console.error('Error getting admin users:', error);
        throw error;
    }
}