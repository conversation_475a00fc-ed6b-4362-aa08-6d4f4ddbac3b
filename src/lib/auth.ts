import bcrypt from 'bcryptjs';
import prisma from './prisma';
import { SecurityLogger, SecurityEventType, logAuthSuccess, logAuthFailure, logAccountLocked } from './security-logger';

const SALT_ROUNDS = 12; // Enhanced from 10 to 12 for better security

// Password complexity validation interface
export interface PasswordValidationResult {
  isValid: boolean;
  errors: string[];
}

// Enhanced password complexity validation with OWASP requirements
export function validatePasswordComplexity(password: string): PasswordValidationResult {
  const errors: string[] = [];

  // Minimum 12 characters
  if (password.length < 12) {
    errors.push('Password must be at least 12 characters long');
  }

  // Must contain uppercase letter
  if (!/[A-Z]/.test(password)) {
    errors.push('Password must contain at least one uppercase letter');
  }

  // Must contain lowercase letter
  if (!/[a-z]/.test(password)) {
    errors.push('Password must contain at least one lowercase letter');
  }

  // Must contain number
  if (!/\d/.test(password)) {
    errors.push('Password must contain at least one number');
  }

  // Must contain special character
  if (!/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
    errors.push('Password must contain at least one special character');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

// Enhanced password hashing with 12 rounds
export async function hashPassword(password: string): Promise<string> {
  // Validate password complexity before hashing
  const validation = validatePasswordComplexity(password);
  if (!validation.isValid) {
    throw new Error(`Password does not meet complexity requirements: ${validation.errors.join(', ')}`);
  }

  return bcrypt.hash(password, SALT_ROUNDS);
}

export async function verifyPassword(password: string, hashedPassword: string): Promise<boolean> {
  return bcrypt.compare(password, hashedPassword);
}

// Check if password was used in the last 5 passwords
export async function checkPasswordHistory(userId: string, newPassword: string): Promise<boolean> {
  const passwordHistory = await prisma.passwordHistory.findMany({
    where: { user_id: userId },
    orderBy: { created_at: 'desc' },
    take: 5
  });

  for (const historyEntry of passwordHistory) {
    const isReused = await verifyPassword(newPassword, historyEntry.password_hash);
    if (isReused) {
      return true; // Password was reused
    }
  }

  return false; // Password is not in recent history
}

// Add password to history after successful change
export async function addPasswordToHistory(userId: string, passwordHash: string): Promise<void> {
  // Add new password to history
  await prisma.passwordHistory.create({
    data: {
      user_id: userId,
      password_hash: passwordHash
    }
  });

  // Keep only the last 5 passwords in history
  const allHistory = await prisma.passwordHistory.findMany({
    where: { user_id: userId },
    orderBy: { created_at: 'desc' }
  });

  if (allHistory.length > 5) {
    const toDelete = allHistory.slice(5);
    await prisma.passwordHistory.deleteMany({
      where: {
        id: {
          in: toDelete.map((h: any) => h.id)
        }
      }
    });
  }
}

// Account lockout interfaces and constants
export interface LockoutStatus {
  isLocked: boolean;
  lockoutExpires?: Date;
  failedAttempts: number;
  remainingTime?: number; // in minutes
}

const MAX_FAILED_ATTEMPTS = 5;
const LOCKOUT_DURATION_MINUTES = 30;

// Check if account is currently locked
export async function checkAccountLockout(email: string): Promise<LockoutStatus> {
  const lockoutRecord = await prisma.accountLockout.findUnique({
    where: { email }
  });

  if (!lockoutRecord) {
    return {
      isLocked: false,
      failedAttempts: 0
    };
  }

  const now = new Date();

  // Check if lockout has expired
  if (lockoutRecord.locked_until && now >= lockoutRecord.locked_until) {
    // Lockout has expired, reset the record
    await prisma.accountLockout.update({
      where: { email },
      data: {
        failed_attempts: 0,
        locked_until: null,
        updated_at: now
      }
    });

    return {
      isLocked: false,
      failedAttempts: 0
    };
  }

  // Account is still locked
  if (lockoutRecord.locked_until && now < lockoutRecord.locked_until) {
    const remainingMs = lockoutRecord.locked_until.getTime() - now.getTime();
    const remainingMinutes = Math.ceil(remainingMs / (1000 * 60));

    return {
      isLocked: true,
      lockoutExpires: lockoutRecord.locked_until,
      failedAttempts: lockoutRecord.failed_attempts,
      remainingTime: remainingMinutes
    };
  }

  return {
    isLocked: false,
    failedAttempts: lockoutRecord.failed_attempts
  };
}

// Record a failed login attempt and potentially lock the account
export async function recordFailedAttempt(email: string, ipAddress: string): Promise<LockoutStatus> {
  const now = new Date();

  // Get or create lockout record
  let lockoutRecord = await prisma.accountLockout.findUnique({
    where: { email }
  });

  if (!lockoutRecord) {
    lockoutRecord = await prisma.accountLockout.create({
      data: {
        email,
        failed_attempts: 1,
        last_attempt_ip: ipAddress,
        created_at: now,
        updated_at: now
      }
    });
  } else {
    // Check if previous lockout has expired
    if (lockoutRecord.locked_until && now >= lockoutRecord.locked_until) {
      // Reset failed attempts if lockout has expired
      lockoutRecord = await prisma.accountLockout.update({
        where: { email },
        data: {
          failed_attempts: 1,
          locked_until: null,
          last_attempt_ip: ipAddress,
          updated_at: now
        }
      });
    } else {
      // Increment failed attempts
      const newFailedAttempts = lockoutRecord.failed_attempts + 1;
      let lockedUntil = lockoutRecord.locked_until;

      // Lock account if max attempts reached
      if (newFailedAttempts >= MAX_FAILED_ATTEMPTS) {
        lockedUntil = new Date(now.getTime() + (LOCKOUT_DURATION_MINUTES * 60 * 1000));
      }

      lockoutRecord = await prisma.accountLockout.update({
        where: { email },
        data: {
          failed_attempts: newFailedAttempts,
          locked_until: lockedUntil,
          last_attempt_ip: ipAddress,
          updated_at: now
        }
      });
    }
  }

  // Return current lockout status
  return await checkAccountLockout(email);
}

// Reset failed attempts after successful login
export async function resetFailedAttempts(email: string): Promise<void> {
  await prisma.accountLockout.updateMany({
    where: { email },
    data: {
      failed_attempts: 0,
      locked_until: null,
      updated_at: new Date()
    }
  });
}

// Clean up expired lockout records (utility function for maintenance)
export async function cleanupExpiredLockouts(): Promise<number> {
  const now = new Date();

  const result = await prisma.accountLockout.updateMany({
    where: {
      locked_until: {
        lte: now
      },
      failed_attempts: {
        gt: 0
      }
    },
    data: {
      failed_attempts: 0,
      locked_until: null,
      updated_at: now
    }
  });

  return result.count;
}

export async function createSuperAdminIfNotExists(ipAddress: string = 'system', userAgent?: string) {
  try {
    // Check if super admin already exists to prevent multiple creation attempts
    const existingSuperAdmin = await prisma.user.findFirst({
      where: { role: 'SUPER_ADMIN' }
    });

    if (existingSuperAdmin) {
      // Log attempt to create duplicate super admin
      await SecurityLogger.logSecurityEvent({
        eventType: SecurityEventType.ADMIN_USER_CREATED,
        email: existingSuperAdmin.email,
        ipAddress,
        userAgent,
        resource: 'super_admin',
        action: 'duplicate_creation_attempt',
        success: false,
        errorMessage: 'Super admin already exists',
        details: {
          existingSuperAdminId: existingSuperAdmin.id,
          attemptReason: 'initialization'
        }
      });

      console.log('Super admin already exists:', existingSuperAdmin.email);
      return existingSuperAdmin;
    }

    // Validate required environment variables
    const requiredEnvVars = ['SUPERADMIN_EMAIL', 'SUPERADMIN_PASSWORD'];
    const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);

    if (missingVars.length > 0) {
      const errorMessage = `Missing required environment variables for super admin setup: ${missingVars.join(', ')}`;

      await SecurityLogger.logSecurityEvent({
        eventType: SecurityEventType.ADMIN_USER_CREATED,
        ipAddress,
        userAgent,
        resource: 'super_admin',
        action: 'initialization_failed',
        success: false,
        errorMessage,
        details: {
          missingVariables: missingVars,
          reason: 'environment_validation_failed'
        }
      });

      throw new Error(errorMessage);
    }

    const firstName = process.env.SUPERADMIN_FIRST_NAME || 'Super';
    const lastName = process.env.SUPERADMIN_LAST_NAME || 'Admin';
    const email = process.env.SUPERADMIN_EMAIL!;
    const password = process.env.SUPERADMIN_PASSWORD!;

    // Enhanced email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      const errorMessage = 'Super admin email format is invalid';

      await SecurityLogger.logSecurityEvent({
        eventType: SecurityEventType.ADMIN_USER_CREATED,
        email,
        ipAddress,
        userAgent,
        resource: 'super_admin',
        action: 'initialization_failed',
        success: false,
        errorMessage,
        details: {
          reason: 'invalid_email_format'
        }
      });

      throw new Error(errorMessage);
    }

    // Validate password complexity with enhanced requirements
    const validation = validatePasswordComplexity(password);
    if (!validation.isValid) {
      const errorMessage = `Super admin password does not meet complexity requirements: ${validation.errors.join(', ')}`;

      await SecurityLogger.logSecurityEvent({
        eventType: SecurityEventType.ADMIN_USER_CREATED,
        email,
        ipAddress,
        userAgent,
        resource: 'super_admin',
        action: 'initialization_failed',
        success: false,
        errorMessage,
        details: {
          reason: 'password_complexity_failed',
          validationErrors: validation.errors
        }
      });

      throw new Error(errorMessage);
    }

    // Check if email is already in use by another user
    const existingUser = await prisma.user.findUnique({
      where: { email }
    });

    if (existingUser) {
      const errorMessage = 'Super admin email is already in use by another user';

      await SecurityLogger.logSecurityEvent({
        eventType: SecurityEventType.ADMIN_USER_CREATED,
        email,
        ipAddress,
        userAgent,
        resource: 'super_admin',
        action: 'initialization_failed',
        success: false,
        errorMessage,
        details: {
          reason: 'email_already_exists',
          existingUserId: existingUser.id,
          existingUserRole: existingUser.role
        }
      });

      throw new Error(errorMessage);
    }

    const hashedPassword = await hashPassword(password);

    // Create super admin with enhanced security fields
    const superAdmin = await prisma.user.create({
      data: {
        email,
        password: hashedPassword,
        first_name: firstName,
        last_name: lastName,
        role: 'SUPER_ADMIN',
        is_active: true,
        password_changed_at: new Date(),
        requires_password_change: false, // Super admin doesn't need to change password on first login
        failed_login_attempts: 0,
        created_at: new Date(),
        updated_at: new Date()
      }
    });

    // Add initial password to history
    await addPasswordToHistory(superAdmin.id, hashedPassword);

    // Log successful super admin creation
    await SecurityLogger.logAdminAction({
      eventType: SecurityEventType.ADMIN_USER_CREATED,
      userId: superAdmin.id, // Self-created
      email: superAdmin.email,
      ipAddress,
      userAgent,
      resource: 'super_admin',
      action: 'initialization_success',
      success: true,
      details: {
        targetUserId: superAdmin.id,
        targetEmail: superAdmin.email,
        newRole: 'SUPER_ADMIN',
        reason: 'system_initialization',
        createdAt: superAdmin.created_at
      }
    });

    console.log('Super admin created successfully:', email);
    return superAdmin;

  } catch (error) {
    console.error('Super admin creation error:', error);

    // Log system error during super admin creation
    await SecurityLogger.logSecurityEvent({
      eventType: SecurityEventType.ADMIN_USER_CREATED,
      ipAddress,
      userAgent,
      resource: 'super_admin',
      action: 'initialization_error',
      success: false,
      errorMessage: error instanceof Error ? error.message : 'Unknown error during super admin creation',
      details: {
        errorType: 'system_error',
        reason: 'super_admin_creation_failed'
      }
    });

    throw error;
  }
}

// Enhanced authentication result interface
export interface AuthenticationResult {
  success: boolean;
  user?: any;
  requiresPasswordChange?: boolean;
  lockoutInfo?: LockoutStatus;
  error?: string;
}

export async function authenticateUser(
  email: string,
  password: string,
  ipAddress: string = 'unknown',
  userAgent?: string
): Promise<AuthenticationResult> {
  try {
    // First check if account is locked
    const lockoutStatus = await checkAccountLockout(email);

    if (lockoutStatus.isLocked) {
      // Log account lockout attempt
      await logAccountLocked(email, ipAddress, lockoutStatus.remainingTime || 0);

      return {
        success: false,
        lockoutInfo: lockoutStatus,
        error: 'Invalid credentials' // Generic error message to prevent user enumeration
      };
    }

    const user = await prisma.user.findUnique({
      where: { email },
      select: {
        id: true,
        email: true,
        password: true,
        first_name: true,
        last_name: true,
        role: true,
        is_active: true,
        requires_password_change: true,
      }
    });

    if (!user) {
      // Record failed attempt for non-existent user to prevent user enumeration timing attacks
      await recordFailedAttempt(email, ipAddress);

      // Log authentication failure with generic reason
      await logAuthFailure(email, ipAddress, userAgent, 'Invalid credentials');

      return {
        success: false,
        error: 'Invalid credentials' // Generic error message
      };
    }

    if (!user.is_active) {
      // Record failed attempt for inactive account
      await recordFailedAttempt(email, ipAddress);

      // Log authentication failure for inactive account
      await logAuthFailure(email, ipAddress, userAgent, 'Account inactive');

      return {
        success: false,
        error: 'Invalid credentials' // Generic error message to prevent user enumeration
      };
    }

    const isValidPassword = await verifyPassword(password, user.password);

    if (!isValidPassword) {
      // Record failed attempt and check if account should be locked
      const newLockoutStatus = await recordFailedAttempt(email, ipAddress);

      // Log authentication failure
      await logAuthFailure(email, ipAddress, userAgent, 'Invalid password');

      if (newLockoutStatus.isLocked) {
        // Log account lockout
        await logAccountLocked(email, ipAddress, newLockoutStatus.remainingTime || 0);

        return {
          success: false,
          lockoutInfo: newLockoutStatus,
          error: 'Invalid credentials' // Generic error message
        };
      }

      return {
        success: false,
        error: 'Invalid credentials' // Generic error message
      };
    }

    // Successful authentication - reset failed attempts
    await resetFailedAttempts(email);

    // Update last login IP and user agent tracking
    await prisma.user.update({
      where: { id: user.id },
      data: {
        last_login_ip: ipAddress,
        // Update last activity timestamp
        updated_at: new Date()
      }
    });

    // Log successful authentication with comprehensive details
    await logAuthSuccess(email, user.id, ipAddress, userAgent, {
      loginMethod: 'password',
      requiresPasswordChange: user.requires_password_change,
      userRole: user.role
    });

    const { password: _, ...userWithoutPassword } = user;

    return {
      success: true,
      user: userWithoutPassword,
      requiresPasswordChange: user.requires_password_change
    };

  } catch (error) {
    console.error('Authentication error:', error);

    // Log system error without exposing details
    await SecurityLogger.logSecurityEvent({
      eventType: SecurityEventType.LOGIN_FAILURE,
      email,
      ipAddress,
      userAgent,
      success: false,
      errorMessage: 'System error during authentication',
      details: {
        errorType: 'system_error',
        hasUser: !!email
      }
    });

    return {
      success: false,
      error: 'Authentication failed' // Generic error message
    };
  }
}

// Session management functions have been moved to session-manager.ts
// This file now focuses only on authentication handlers
