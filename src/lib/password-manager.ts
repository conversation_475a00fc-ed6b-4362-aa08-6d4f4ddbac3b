import crypto from 'crypto';
import prisma from './prisma';
import { hashPassword, validatePasswordComplexity, checkPasswordHistory, addPasswordToHistory } from './auth';
import { sessionManager } from './session-manager';
import { SecurityLogger, SecurityEventType } from './security-logger';

// Password reset token interfaces
export interface PasswordResetToken {
    id: string;
    token: string;
    userId: string;
    expiresAt: Date;
    isUsed: boolean;
    createdAt: Date;
}

export interface PasswordResetRequest {
    email: string;
    ipAddress: string;
    userAgent?: string;
}

export interface PasswordResetValidation {
    isValid: boolean;
    userId?: string;
    error?: string;
    token?: PasswordResetToken;
}

export interface PasswordChangeRequest {
    userId: string;
    currentPassword: string;
    newPassword: string;
    ipAddress: string;
    userAgent?: string;
    currentSessionToken?: string;
}

export interface PasswordChangeResult {
    success: boolean;
    error?: string;
    invalidatedSessions?: number;
    requiresReauth?: boolean;
}

export interface ForcedPasswordChangeRequest {
    userId: string;
    newPassword: string;
    adminUserId: string;
    reason: string;
    ipAddress: string;
    userAgent?: string;
}

// Password reset token constants
const RESET_TOKEN_LENGTH = 64; // bytes for cryptographically secure token
const RESET_TOKEN_EXPIRY_HOURS = 1;
const MAX_RESET_ATTEMPTS_PER_HOUR = 3;

export class PasswordManager {
    /**
     * Generate cryptographically secure password reset token
     */
    private generateResetToken(): string {
        return crypto.randomBytes(RESET_TOKEN_LENGTH).toString('hex');
    }

    /**
     * Check rate limiting for password reset requests
     */
    private async checkResetRateLimit(email: string, ipAddress: string): Promise<boolean> {
        const oneHourAgo = new Date(Date.now() - (60 * 60 * 1000));

        // Check reset attempts by email
        const emailAttempts = await prisma.passwordResetToken.count({
            where: {
                user: { email },
                created_at: { gte: oneHourAgo }
            }
        });

        // Check reset attempts by IP address
        const ipAttempts = await prisma.securityAuditLog.count({
            where: {
                event_type: 'PASSWORD_RESET_REQUESTED',
                ip_address: ipAddress,
                created_at: { gte: oneHourAgo }
            }
        });

        return emailAttempts < MAX_RESET_ATTEMPTS_PER_HOUR && ipAttempts < (MAX_RESET_ATTEMPTS_PER_HOUR * 3);
    }

    /**
     * Generate password reset token with 1-hour expiration
     */
    async generatePasswordResetToken(request: PasswordResetRequest): Promise<{
        success: boolean;
        token?: string;
        expiresAt?: Date;
        error?: string;
    }> {
        const { email, ipAddress, userAgent } = request;

        try {
            // Check rate limiting
            const rateLimitOk = await this.checkResetRateLimit(email, ipAddress);
            if (!rateLimitOk) {
                await SecurityLogger.logSecurityEvent({
                    eventType: SecurityEventType.PASSWORD_RESET_REQUESTED,
                    email,
                    ipAddress,
                    userAgent,
                    success: false,
                    errorMessage: 'Rate limit exceeded for password reset requests',
                    details: {
                        reason: 'rate_limit_exceeded',
                        maxAttemptsPerHour: MAX_RESET_ATTEMPTS_PER_HOUR
                    }
                });

                return {
                    success: false,
                    error: 'Too many password reset requests. Please try again later.'
                };
            }

            // Find user by email
            const user = await prisma.user.findUnique({
                where: { email },
                select: { id: true, email: true, is_active: true }
            });

            if (!user) {
                // Log attempt for non-existent user but don't reveal this information
                await SecurityLogger.logSecurityEvent({
                    eventType: SecurityEventType.PASSWORD_RESET_REQUESTED,
                    email,
                    ipAddress,
                    userAgent,
                    success: false,
                    errorMessage: 'Password reset requested for non-existent user',
                    details: {
                        reason: 'user_not_found'
                    }
                });

                // Return success to prevent user enumeration
                return {
                    success: true,
                    token: 'dummy_token', // This won't be used
                    expiresAt: new Date(Date.now() + (RESET_TOKEN_EXPIRY_HOURS * 60 * 60 * 1000))
                };
            }

            if (!user.is_active) {
                await SecurityLogger.logSecurityEvent({
                    eventType: SecurityEventType.PASSWORD_RESET_REQUESTED,
                    userId: user.id,
                    email,
                    ipAddress,
                    userAgent,
                    success: false,
                    errorMessage: 'Password reset requested for inactive user',
                    details: {
                        reason: 'user_inactive'
                    }
                });

                // Return success to prevent user enumeration
                return {
                    success: true,
                    token: 'dummy_token',
                    expiresAt: new Date(Date.now() + (RESET_TOKEN_EXPIRY_HOURS * 60 * 60 * 1000))
                };
            }

            // Invalidate any existing reset tokens for this user
            await prisma.passwordResetToken.updateMany({
                where: {
                    user_id: user.id,
                    is_used: false,
                    expires_at: { gt: new Date() }
                },
                data: { is_used: true }
            });

            // Generate new reset token
            const token = this.generateResetToken();
            const expiresAt = new Date(Date.now() + (RESET_TOKEN_EXPIRY_HOURS * 60 * 60 * 1000));

            // Store reset token
            await prisma.passwordResetToken.create({
                data: {
                    token,
                    user_id: user.id,
                    expires_at: expiresAt,
                    is_used: false,
                    created_at: new Date()
                }
            });

            // Log successful token generation
            await SecurityLogger.logSecurityEvent({
                eventType: SecurityEventType.PASSWORD_RESET_REQUESTED,
                userId: user.id,
                email,
                ipAddress,
                userAgent,
                success: true,
                details: {
                    tokenExpiresAt: expiresAt.toISOString(),
                    reason: 'password_reset_token_generated'
                }
            });

            return {
                success: true,
                token,
                expiresAt
            };

        } catch (error) {
            console.error('Password reset token generation error:', error);

            await SecurityLogger.logSecurityEvent({
                eventType: SecurityEventType.PASSWORD_RESET_REQUESTED,
                email,
                ipAddress,
                userAgent,
                success: false,
                errorMessage: 'System error during password reset token generation',
                details: {
                    errorType: 'system_error',
                    reason: 'token_generation_failed'
                }
            });

            return {
                success: false,
                error: 'Unable to process password reset request. Please try again later.'
            };
        }
    }

    /**
     * Validate password reset token
     */
    async validatePasswordResetToken(token: string): Promise<PasswordResetValidation> {
        try {
            const resetToken = await prisma.passwordResetToken.findUnique({
                where: { token },
                include: {
                    user: {
                        select: {
                            id: true,
                            email: true,
                            is_active: true
                        }
                    }
                }
            });

            if (!resetToken) {
                return {
                    isValid: false,
                    error: 'Invalid or expired reset token'
                };
            }

            if (resetToken.is_used) {
                return {
                    isValid: false,
                    error: 'Reset token has already been used'
                };
            }

            if (new Date() > resetToken.expires_at) {
                // Mark token as used to prevent reuse
                await prisma.passwordResetToken.update({
                    where: { id: resetToken.id },
                    data: { is_used: true }
                });

                return {
                    isValid: false,
                    error: 'Reset token has expired'
                };
            }

            if (!resetToken.user.is_active) {
                return {
                    isValid: false,
                    error: 'User account is not active'
                };
            }

            return {
                isValid: true,
                userId: resetToken.user.id,
                token: {
                    id: resetToken.id,
                    token: resetToken.token,
                    userId: resetToken.user_id,
                    expiresAt: resetToken.expires_at,
                    isUsed: resetToken.is_used,
                    createdAt: resetToken.created_at
                }
            };

        } catch (error) {
            console.error('Password reset token validation error:', error);
            return {
                isValid: false,
                error: 'Unable to validate reset token'
            };
        }
    }

    /**
     * Reset password using valid token
     */
    async resetPasswordWithToken(
        token: string,
        newPassword: string,
        ipAddress: string,
        userAgent?: string
    ): Promise<{
        success: boolean;
        error?: string;
        userId?: string;
    }> {
        try {
            // Validate token
            const tokenValidation = await this.validatePasswordResetToken(token);
            if (!tokenValidation.isValid || !tokenValidation.userId) {
                await SecurityLogger.logSecurityEvent({
                    eventType: SecurityEventType.PASSWORD_RESET_COMPLETED,
                    ipAddress,
                    userAgent,
                    success: false,
                    errorMessage: tokenValidation.error || 'Invalid token',
                    details: {
                        reason: 'invalid_token',
                        token: token.substring(0, 8) + '...' // Log partial token for debugging
                    }
                });

                return {
                    success: false,
                    error: tokenValidation.error || 'Invalid reset token'
                };
            }

            const userId = tokenValidation.userId;

            // Validate new password complexity
            const passwordValidation = validatePasswordComplexity(newPassword);
            if (!passwordValidation.isValid) {
                await SecurityLogger.logSecurityEvent({
                    eventType: SecurityEventType.PASSWORD_RESET_COMPLETED,
                    userId,
                    ipAddress,
                    userAgent,
                    success: false,
                    errorMessage: 'Password complexity requirements not met',
                    details: {
                        reason: 'password_complexity_failed',
                        validationErrors: passwordValidation.errors
                    }
                });

                return {
                    success: false,
                    error: `Password does not meet complexity requirements: ${passwordValidation.errors.join(', ')}`
                };
            }

            // Check password history to prevent reuse
            const isPasswordReused = await checkPasswordHistory(userId, newPassword);
            if (isPasswordReused) {
                await SecurityLogger.logSecurityEvent({
                    eventType: SecurityEventType.PASSWORD_RESET_COMPLETED,
                    userId,
                    ipAddress,
                    userAgent,
                    success: false,
                    errorMessage: 'Password was recently used',
                    details: {
                        reason: 'password_reuse_detected'
                    }
                });

                return {
                    success: false,
                    error: 'Password was recently used. Please choose a different password.'
                };
            }

            // Hash new password
            const hashedPassword = await hashPassword(newPassword);

            // Update user password and related fields
            await prisma.user.update({
                where: { id: userId },
                data: {
                    password: hashedPassword,
                    password_changed_at: new Date(),
                    requires_password_change: false,
                    failed_login_attempts: 0,
                    locked_until: null
                }
            });

            // Add password to history
            await addPasswordToHistory(userId, hashedPassword);

            // Mark reset token as used
            if (tokenValidation.token) {
                await prisma.passwordResetToken.update({
                    where: { id: tokenValidation.token.id },
                    data: { is_used: true }
                });
            }

            // Invalidate all existing sessions for security
            const invalidatedSessions = await sessionManager.invalidateSessionsForSecurityEvents(
                userId,
                'password_change'
            );

            // Reset account lockout if exists
            await prisma.accountLockout.updateMany({
                where: { email: { in: [await this.getUserEmail(userId)] } },
                data: {
                    failed_attempts: 0,
                    locked_until: null
                }
            });

            // Log successful password reset
            await SecurityLogger.logSecurityEvent({
                eventType: SecurityEventType.PASSWORD_RESET_COMPLETED,
                userId,
                ipAddress,
                userAgent,
                success: true,
                details: {
                    reason: 'password_reset_successful',
                    invalidatedSessions: invalidatedSessions.invalidatedCount,
                    tokenUsed: token.substring(0, 8) + '...'
                }
            });

            return {
                success: true,
                userId
            };

        } catch (error) {
            console.error('Password reset error:', error);

            await SecurityLogger.logSecurityEvent({
                eventType: SecurityEventType.PASSWORD_RESET_COMPLETED,
                ipAddress,
                userAgent,
                success: false,
                errorMessage: 'System error during password reset',
                details: {
                    errorType: 'system_error',
                    reason: 'password_reset_failed'
                }
            });

            return {
                success: false,
                error: 'Unable to reset password. Please try again later.'
            };
        }
    }

    /**
     * Change password with current password verification
     */
    async changePassword(request: PasswordChangeRequest): Promise<PasswordChangeResult> {
        const { userId, currentPassword, newPassword, ipAddress, userAgent, currentSessionToken } = request;

        try {
            // Get user with current password
            const user = await prisma.user.findUnique({
                where: { id: userId },
                select: {
                    id: true,
                    email: true,
                    password: true,
                    is_active: true,
                    requires_password_change: true
                }
            });

            if (!user) {
                await SecurityLogger.logSecurityEvent({
                    eventType: SecurityEventType.PASSWORD_CHANGED,
                    userId,
                    ipAddress,
                    userAgent,
                    success: false,
                    errorMessage: 'User not found for password change',
                    details: {
                        reason: 'user_not_found'
                    }
                });

                return {
                    success: false,
                    error: 'User not found'
                };
            }

            if (!user.is_active) {
                await SecurityLogger.logSecurityEvent({
                    eventType: SecurityEventType.PASSWORD_CHANGED,
                    userId,
                    email: user.email,
                    ipAddress,
                    userAgent,
                    success: false,
                    errorMessage: 'Password change attempted for inactive user',
                    details: {
                        reason: 'user_inactive'
                    }
                });

                return {
                    success: false,
                    error: 'Account is not active'
                };
            }

            // Verify current password
            const bcrypt = await import('bcryptjs');
            const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.password);

            if (!isCurrentPasswordValid) {
                await SecurityLogger.logSecurityEvent({
                    eventType: SecurityEventType.PASSWORD_CHANGED,
                    userId,
                    email: user.email,
                    ipAddress,
                    userAgent,
                    success: false,
                    errorMessage: 'Invalid current password provided',
                    details: {
                        reason: 'invalid_current_password'
                    }
                });

                return {
                    success: false,
                    error: 'Current password is incorrect'
                };
            }

            // Validate new password complexity
            const passwordValidation = validatePasswordComplexity(newPassword);
            if (!passwordValidation.isValid) {
                await SecurityLogger.logSecurityEvent({
                    eventType: SecurityEventType.PASSWORD_CHANGED,
                    userId,
                    email: user.email,
                    ipAddress,
                    userAgent,
                    success: false,
                    errorMessage: 'New password does not meet complexity requirements',
                    details: {
                        reason: 'password_complexity_failed',
                        validationErrors: passwordValidation.errors
                    }
                });

                return {
                    success: false,
                    error: `New password does not meet complexity requirements: ${passwordValidation.errors.join(', ')}`
                };
            }

            // Check if new password is same as current
            const isSamePassword = await bcrypt.compare(newPassword, user.password);
            if (isSamePassword) {
                await SecurityLogger.logSecurityEvent({
                    eventType: SecurityEventType.PASSWORD_CHANGED,
                    userId,
                    email: user.email,
                    ipAddress,
                    userAgent,
                    success: false,
                    errorMessage: 'New password is same as current password',
                    details: {
                        reason: 'same_password'
                    }
                });

                return {
                    success: false,
                    error: 'New password must be different from current password'
                };
            }

            // Check password history to prevent reuse
            const isPasswordReused = await checkPasswordHistory(userId, newPassword);
            if (isPasswordReused) {
                await SecurityLogger.logSecurityEvent({
                    eventType: SecurityEventType.PASSWORD_CHANGED,
                    userId,
                    email: user.email,
                    ipAddress,
                    userAgent,
                    success: false,
                    errorMessage: 'New password was recently used',
                    details: {
                        reason: 'password_reuse_detected'
                    }
                });

                return {
                    success: false,
                    error: 'Password was recently used. Please choose a different password.'
                };
            }

            // Hash new password
            const hashedPassword = await hashPassword(newPassword);

            // Update user password and related fields
            await prisma.user.update({
                where: { id: userId },
                data: {
                    password: hashedPassword,
                    password_changed_at: new Date(),
                    requires_password_change: false,
                    failed_login_attempts: 0,
                    locked_until: null
                }
            });

            // Add password to history
            await addPasswordToHistory(userId, hashedPassword);

            // Invalidate all other sessions except current one for security
            const invalidatedSessions = await sessionManager.invalidateSessionsForSecurityEvents(
                userId,
                'password_change',
                currentSessionToken
            );

            // Reset account lockout if exists
            await prisma.accountLockout.updateMany({
                where: { email: user.email },
                data: {
                    failed_attempts: 0,
                    locked_until: null
                }
            });

            // Log successful password change
            await SecurityLogger.logSecurityEvent({
                eventType: SecurityEventType.PASSWORD_CHANGED,
                userId,
                email: user.email,
                ipAddress,
                userAgent,
                success: true,
                details: {
                    reason: 'password_change_successful',
                    invalidatedSessions: invalidatedSessions.invalidatedCount,
                    currentSessionPreserved: !!currentSessionToken
                }
            });

            return {
                success: true,
                invalidatedSessions: invalidatedSessions.invalidatedCount,
                requiresReauth: false // Current session is preserved
            };

        } catch (error) {
            console.error('Password change error:', error);

            await SecurityLogger.logSecurityEvent({
                eventType: SecurityEventType.PASSWORD_CHANGED,
                userId,
                ipAddress,
                userAgent,
                success: false,
                errorMessage: 'System error during password change',
                details: {
                    errorType: 'system_error',
                    reason: 'password_change_failed'
                }
            });

            return {
                success: false,
                error: 'Unable to change password. Please try again later.'
            };
        }
    }

    /**
     * Force password change (admin action)
     */
    async forcePasswordChange(request: ForcedPasswordChangeRequest): Promise<{
        success: boolean;
        error?: string;
        invalidatedSessions?: number;
    }> {
        const { userId, newPassword, adminUserId, reason, ipAddress, userAgent } = request;

        try {
            // Verify admin has permission to force password change
            const admin = await prisma.user.findUnique({
                where: { id: adminUserId },
                select: { id: true, email: true, role: true, is_active: true }
            });

            if (!admin || !admin.is_active) {
                await SecurityLogger.logSecurityEvent({
                    eventType: SecurityEventType.PASSWORD_CHANGED,
                    userId: adminUserId,
                    ipAddress,
                    userAgent,
                    success: false,
                    errorMessage: 'Invalid admin user for forced password change',
                    details: {
                        reason: 'invalid_admin',
                        targetUserId: userId
                    }
                });

                return {
                    success: false,
                    error: 'Unauthorized to perform this action'
                };
            }

            // Check admin permissions (SUPER_ADMIN can change any password, ADMIN can change non-admin passwords)
            const targetUser = await prisma.user.findUnique({
                where: { id: userId },
                select: { id: true, email: true, role: true, is_active: true }
            });

            if (!targetUser) {
                await SecurityLogger.logSecurityEvent({
                    eventType: SecurityEventType.PASSWORD_CHANGED,
                    userId: adminUserId,
                    email: admin.email,
                    ipAddress,
                    userAgent,
                    success: false,
                    errorMessage: 'Target user not found for forced password change',
                    details: {
                        reason: 'target_user_not_found',
                        targetUserId: userId
                    }
                });

                return {
                    success: false,
                    error: 'Target user not found'
                };
            }

            // Permission check: SUPER_ADMIN can change anyone's password, DEV can change MARKETING/SALES passwords
            if (admin.role !== 'SUPER_ADMIN' && (targetUser.role === 'SUPER_ADMIN' ||
                (admin.role === 'DEV' && targetUser.role === 'DEV') ||
                (admin.role === 'MARKETING' && (targetUser.role === 'DEV' || targetUser.role === 'MARKETING')) ||
                (admin.role === 'SALES' && (targetUser.role === 'DEV' || targetUser.role === 'MARKETING' || targetUser.role === 'SALES')))) {
                await SecurityLogger.logSecurityEvent({
                    eventType: SecurityEventType.PASSWORD_CHANGED,
                    userId: adminUserId,
                    email: admin.email,
                    ipAddress,
                    userAgent,
                    success: false,
                    errorMessage: 'Insufficient permissions for forced password change',
                    details: {
                        reason: 'insufficient_permissions',
                        adminRole: admin.role,
                        targetRole: targetUser.role,
                        targetUserId: userId
                    }
                });

                return {
                    success: false,
                    error: 'Insufficient permissions to change this user\'s password'
                };
            }

            // Validate new password complexity
            const passwordValidation = validatePasswordComplexity(newPassword);
            if (!passwordValidation.isValid) {
                await SecurityLogger.logSecurityEvent({
                    eventType: SecurityEventType.PASSWORD_CHANGED,
                    userId: adminUserId,
                    email: admin.email,
                    ipAddress,
                    userAgent,
                    success: false,
                    errorMessage: 'New password does not meet complexity requirements',
                    details: {
                        reason: 'password_complexity_failed',
                        validationErrors: passwordValidation.errors,
                        targetUserId: userId,
                        forceChangeReason: reason
                    }
                });

                return {
                    success: false,
                    error: `New password does not meet complexity requirements: ${passwordValidation.errors.join(', ')}`
                };
            }

            // Check password history to prevent reuse
            const isPasswordReused = await checkPasswordHistory(userId, newPassword);
            if (isPasswordReused) {
                await SecurityLogger.logSecurityEvent({
                    eventType: SecurityEventType.PASSWORD_CHANGED,
                    userId: adminUserId,
                    email: admin.email,
                    ipAddress,
                    userAgent,
                    success: false,
                    errorMessage: 'New password was recently used by target user',
                    details: {
                        reason: 'password_reuse_detected',
                        targetUserId: userId,
                        forceChangeReason: reason
                    }
                });

                return {
                    success: false,
                    error: 'Password was recently used. Please choose a different password.'
                };
            }

            // Hash new password
            const hashedPassword = await hashPassword(newPassword);

            // Update user password and force password change on next login
            await prisma.user.update({
                where: { id: userId },
                data: {
                    password: hashedPassword,
                    password_changed_at: new Date(),
                    requires_password_change: true, // Force user to change password on next login
                    failed_login_attempts: 0,
                    locked_until: null
                }
            });

            // Add password to history
            await addPasswordToHistory(userId, hashedPassword);

            // Invalidate all sessions for the target user
            const invalidatedSessions = await sessionManager.invalidateSessionsForSecurityEvents(
                userId,
                'admin_action'
            );

            // Reset account lockout if exists
            await prisma.accountLockout.updateMany({
                where: { email: targetUser.email },
                data: {
                    failed_attempts: 0,
                    locked_until: null
                }
            });

            // Log successful forced password change
            await SecurityLogger.logAdminAction({
                eventType: SecurityEventType.PASSWORD_CHANGED,
                userId: adminUserId,
                email: admin.email,
                ipAddress,
                userAgent,
                resource: 'user_password',
                action: 'forced_password_change',
                success: true,
                details: {
                    targetUserId: userId,
                    targetEmail: targetUser.email,
                    reason,
                    invalidatedSessions: invalidatedSessions.invalidatedCount,
                    requiresPasswordChangeOnLogin: true
                }
            });

            return {
                success: true,
                invalidatedSessions: invalidatedSessions.invalidatedCount
            };

        } catch (error) {
            console.error('Forced password change error:', error);

            await SecurityLogger.logSecurityEvent({
                eventType: SecurityEventType.PASSWORD_CHANGED,
                userId: adminUserId,
                ipAddress,
                userAgent,
                success: false,
                errorMessage: 'System error during forced password change',
                details: {
                    errorType: 'system_error',
                    reason: 'forced_password_change_failed',
                    targetUserId: userId
                }
            });

            return {
                success: false,
                error: 'Unable to change password. Please try again later.'
            };
        }
    }

    /**
     * Set user to require password change on next login
     */
    async requirePasswordChangeOnNextLogin(
        userId: string,
        adminUserId: string,
        reason: string,
        ipAddress: string,
        userAgent?: string
    ): Promise<{
        success: boolean;
        error?: string;
    }> {
        try {
            // Verify admin permissions
            const admin = await prisma.user.findUnique({
                where: { id: adminUserId },
                select: { id: true, email: true, role: true, is_active: true }
            });

            if (!admin || !admin.is_active || (admin.role !== 'SUPER_ADMIN' && admin.role !== 'ADMIN')) {
                return {
                    success: false,
                    error: 'Unauthorized to perform this action'
                };
            }

            // Get target user
            const targetUser = await prisma.user.findUnique({
                where: { id: userId },
                select: { id: true, email: true, role: true }
            });

            if (!targetUser) {
                return {
                    success: false,
                    error: 'Target user not found'
                };
            }

            // Update user to require password change
            await prisma.user.update({
                where: { id: userId },
                data: {
                    requires_password_change: true
                }
            });

            // Log admin action
            await SecurityLogger.logAdminAction({
                eventType: SecurityEventType.ADMIN_USER_UPDATED,
                userId: adminUserId,
                email: admin.email,
                ipAddress,
                userAgent,
                resource: 'user_password_policy',
                action: 'require_password_change',
                success: true,
                details: {
                    targetUserId: userId,
                    targetEmail: targetUser.email,
                    reason
                }
            });

            return { success: true };

        } catch (error) {
            console.error('Require password change error:', error);
            return {
                success: false,
                error: 'Unable to update password requirements'
            };
        }
    }

    /**
     * Clean up expired password reset tokens (maintenance function)
     */
    async cleanupExpiredResetTokens(): Promise<number> {
        try {
            const result = await prisma.passwordResetToken.updateMany({
                where: {
                    expires_at: { lte: new Date() },
                    is_used: false
                },
                data: { is_used: true }
            });

            return result.count;
        } catch (error) {
            console.error('Cleanup expired reset tokens error:', error);
            return 0;
        }
    }

    /**
     * Get password security metrics for monitoring
     */
    async getPasswordSecurityMetrics(): Promise<{
        activeResetTokens: number;
        expiredResetTokens: number;
        usersRequiringPasswordChange: number;
        recentPasswordChanges: number;
        passwordHistoryEntries: number;
    }> {
        try {
            const now = new Date();
            const oneDayAgo = new Date(now.getTime() - (24 * 60 * 60 * 1000));

            const [
                activeResetTokens,
                expiredResetTokens,
                usersRequiringPasswordChange,
                recentPasswordChanges,
                passwordHistoryEntries
            ] = await Promise.all([
                prisma.passwordResetToken.count({
                    where: {
                        is_used: false,
                        expires_at: { gt: now }
                    }
                }),
                prisma.passwordResetToken.count({
                    where: {
                        is_used: false,
                        expires_at: { lte: now }
                    }
                }),
                prisma.user.count({
                    where: {
                        requires_password_change: true,
                        is_active: true
                    }
                }),
                prisma.user.count({
                    where: {
                        password_changed_at: { gte: oneDayAgo }
                    }
                }),
                prisma.passwordHistory.count()
            ]);

            return {
                activeResetTokens,
                expiredResetTokens,
                usersRequiringPasswordChange,
                recentPasswordChanges,
                passwordHistoryEntries
            };
        } catch (error) {
            console.error('Get password security metrics error:', error);
            return {
                activeResetTokens: 0,
                expiredResetTokens: 0,
                usersRequiringPasswordChange: 0,
                recentPasswordChanges: 0,
                passwordHistoryEntries: 0
            };
        }
    }

    /**
     * Helper method to get user email by ID
     */
    private async getUserEmail(userId: string): Promise<string> {
        const user = await prisma.user.findUnique({
            where: { id: userId },
            select: { email: true }
        });
        return user?.email || '';
    }
}

// Export singleton instance
export const passwordManager = new PasswordManager();

// Export utility functions for backward compatibility
export async function generatePasswordResetToken(request: PasswordResetRequest) {
    return passwordManager.generatePasswordResetToken(request);
}

export async function validatePasswordResetToken(token: string) {
    return passwordManager.validatePasswordResetToken(token);
}

export async function resetPasswordWithToken(
    token: string,
    newPassword: string,
    ipAddress: string,
    userAgent?: string
) {
    return passwordManager.resetPasswordWithToken(token, newPassword, ipAddress, userAgent);
}

export async function changePassword(request: PasswordChangeRequest) {
    return passwordManager.changePassword(request);
}

export async function forcePasswordChange(request: ForcedPasswordChangeRequest) {
    return passwordManager.forcePasswordChange(request);
}

export async function cleanupExpiredResetTokens() {
    return passwordManager.cleanupExpiredResetTokens();
}