import { prisma } from './prisma';
import { SecurityLogger, SecurityEventType } from './security-logger';

// Import AdminAction from types to ensure consistency
import { AdminAction } from '../types';

// Additional actions specific to activity logging that may not be in the main enum
export enum ActivityAction {
  // Profile management actions
  UPDATE_PROFILE = 'UPDATE_PROFILE',

  // Session management actions
  REVOKE_SESSION = 'REVOKE_SESSION',
  REVOKE_ALL_SESSIONS = 'REVOKE_ALL_SESSIONS',

  // System management actions
  UPDATE_SYSTEM_SETTING = 'UPDATE_SYSTEM_SETTING',
}

// Activity log entry interface
export interface ActivityLogEntry {
  userId: string;
  action: AdminAction | ActivityAction;
  resource: string;
  resourceId?: string;
  details?: any;
  ipAddress?: string;
  userAgent?: string;
  success?: boolean;
  errorMessage?: string;
}

/**
 * Activity Logger for tracking admin and user actions
 * 
 * This module provides centralized activity logging that integrates
 * with the security audit system while maintaining backward compatibility
 * with existing code that expects an ActivityLogger.
 */
export class ActivityLogger {
  /**
   * Log an activity/admin action
   */
  static async log(entry: ActivityLogEntry): Promise<void> {
    try {
      // Map AdminAction to SecurityEventType for security logging
      const eventType = this.mapActionToSecurityEventType(entry.action);

      // Log to security audit system
      await SecurityLogger.logSecurityEvent({
        eventType,
        userId: entry.userId,
        ipAddress: entry.ipAddress || 'unknown',
        userAgent: entry.userAgent,
        resource: entry.resource,
        action: entry.action,
        success: entry.success !== false, // Default to true unless explicitly false
        errorMessage: entry.errorMessage,
        details: {
          resourceId: entry.resourceId,
          actionDetails: entry.details
        }
      });

      // Also log to console for development
      if (process.env.NODE_ENV === 'development') {
        console.log(`[ACTIVITY] ${entry.action}: User ${entry.userId} on ${entry.resource}${entry.resourceId ? ` (${entry.resourceId})` : ''}`);
      }
    } catch (error) {
      console.error('Failed to log activity:', error);
      // Don't throw - logging failures shouldn't break the main operation
    }
  }

  /**
   * Map AdminAction or ActivityAction to SecurityEventType
   */
  private static mapActionToSecurityEventType(action: AdminAction | ActivityAction): SecurityEventType {
    switch (action) {
      case AdminAction.LOGIN:
        return SecurityEventType.LOGIN_SUCCESS;
      case AdminAction.LOGOUT:
        return SecurityEventType.LOGOUT;
      case AdminAction.CREATE_USER:
        return SecurityEventType.ADMIN_USER_CREATED;
      case AdminAction.UPDATE_USER:
      case ActivityAction.UPDATE_PROFILE:
        return SecurityEventType.ADMIN_USER_UPDATED;
      case AdminAction.DELETE_USER:
        return SecurityEventType.ADMIN_USER_DEACTIVATED;
      case ActivityAction.REVOKE_SESSION:
      case ActivityAction.REVOKE_ALL_SESSIONS:
        return SecurityEventType.SESSION_INVALIDATED;
      case AdminAction.UPDATE_SETTINGS:
      case ActivityAction.UPDATE_SYSTEM_SETTING:
        return SecurityEventType.ADMIN_PERMISSIONS_MODIFIED;
      case AdminAction.PASSWORD_RESET:
        return SecurityEventType.PASSWORD_RESET_SUCCESS;
      case AdminAction.SEARCH_IP:
      case AdminAction.LOOKUP_IP:
      case AdminAction.VIEW_API_LOGS:
      case AdminAction.EXPORT_DATA:
      case AdminAction.BULK_OPERATION:
      case AdminAction.ASSIGN_TASK:
      case AdminAction.COMPLETE_TASK:
      default:
        return SecurityEventType.ADMIN_PERMISSIONS_MODIFIED; // Generic admin action
    }
  }

  /**
   * Get activity logs for a user
   */
  static async getUserActivities(
    userId: string,
    options: {
      limit?: number;
      offset?: number;
      startDate?: Date;
      endDate?: Date;
      actions?: (AdminAction | ActivityAction)[];
    } = {}
  ): Promise<any[]> {
    try {
      const {
        limit = 50,
        offset = 0,
        startDate,
        endDate,
        actions
      } = options;

      const where: any = {
        user_id: userId
      };

      if (startDate || endDate) {
        where.created_at = {};
        if (startDate) where.created_at.gte = startDate;
        if (endDate) where.created_at.lte = endDate;
      }

      if (actions && actions.length > 0) {
        where.action = { in: actions };
      }

      const activities = await prisma.securityAuditLog.findMany({
        where,
        orderBy: { created_at: 'desc' },
        take: limit,
        skip: offset,
        select: {
          id: true,
          event_type: true,
          action: true,
          resource: true,
          details: true,
          ip_address: true,
          user_agent: true,
          success: true,
          error_message: true,
          created_at: true
        }
      });

      return activities.map(activity => ({
        id: activity.id,
        action: activity.action,
        resource: activity.resource,
        resourceId: activity.details?.resourceId,
        details: activity.details?.actionDetails,
        ipAddress: activity.ip_address,
        userAgent: activity.user_agent,
        success: activity.success,
        errorMessage: activity.error_message,
        createdAt: activity.created_at
      }));
    } catch (error) {
      console.error('Failed to get user activities:', error);
      return [];
    }
  }

  /**
   * Get activity statistics for a user
   */
  static async getUserActivityStats(
    userId: string,
    timeRange: 'day' | 'week' | 'month' = 'week'
  ): Promise<{
    totalActivities: number;
    actionBreakdown: Record<string, number>;
    resourceBreakdown: Record<string, number>;
  }> {
    try {
      const startDate = new Date();
      switch (timeRange) {
        case 'day':
          startDate.setDate(startDate.getDate() - 1);
          break;
        case 'week':
          startDate.setDate(startDate.getDate() - 7);
          break;
        case 'month':
          startDate.setMonth(startDate.getMonth() - 1);
          break;
      }

      const activities = await prisma.securityAuditLog.findMany({
        where: {
          user_id: userId,
          created_at: { gte: startDate }
        },
        select: {
          action: true,
          resource: true
        }
      });

      const actionBreakdown: Record<string, number> = {};
      const resourceBreakdown: Record<string, number> = {};

      activities.forEach(activity => {
        if (activity.action) {
          actionBreakdown[activity.action] = (actionBreakdown[activity.action] || 0) + 1;
        }
        if (activity.resource) {
          resourceBreakdown[activity.resource] = (resourceBreakdown[activity.resource] || 0) + 1;
        }
      });

      return {
        totalActivities: activities.length,
        actionBreakdown,
        resourceBreakdown
      };
    } catch (error) {
      console.error('Failed to get user activity stats:', error);
      return {
        totalActivities: 0,
        actionBreakdown: {},
        resourceBreakdown: {}
      };
    }
  }
}

// Export singleton instance for backward compatibility
export const activityLogger = new ActivityLogger();
