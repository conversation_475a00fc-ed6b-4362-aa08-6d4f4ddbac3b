import { Request, Response } from 'express';
import { authenticateUser, createSession, invalidateSession } from '../lib/auth';
import { sessionManager } from '../lib/session-manager';
import { changePassword, resetPasswordWithToken, generatePasswordResetToken } from '../lib/password-manager';
import { createAdminUser } from '../lib/admin-manager';
import { SecurityLogger, SecurityEventType } from '../lib/security-logger';
import { validatePasswordComplexity } from '../lib/auth';
import { ApiResponse } from '../types';
import crypto from 'crypto';
import speakeasy from 'speakeasy';
import QRCode from 'qrcode';
import prisma from '../lib/prisma';

// Helper function to get client IP
function getClientIp(req: Request): string {
    const forwarded = req.headers['x-forwarded-for'];
    const realIp = req.headers['x-real-ip'];
    const cfConnectingIp = req.headers['cf-connecting-ip'];

    if (typeof forwarded === 'string') {
        return forwarded.split(',')[0].trim();
    }

    if (typeof realIp === 'string') {
        return realIp;
    }

    if (typeof cfConnectingIp === 'string') {
        return cfConnectingIp;
    }

    return req.connection?.remoteAddress ||
        req.socket?.remoteAddress ||
        '127.0.0.1';
}

// Helper function to get user agent
function getUserAgent(req: Request): string {
    return req.headers['user-agent'] || 'Unknown';
}

/**
 * Enhanced login endpoint with security controls and logging
 * POST /api/auth/login
 */
export async function loginHandler(req: Request, res: Response): Promise<void> {
    const { email, password } = req.body;
    const ipAddress = getClientIp(req);
    const userAgent = getUserAgent(req);

    try {
        // Input validation
        if (!email || !password) {
            await SecurityLogger.logSecurityEvent({
                eventType: SecurityEventType.LOGIN_FAILURE,
                email: email || 'unknown',
                ipAddress,
                userAgent,
                success: false,
                errorMessage: 'Missing credentials',
                details: {
                    reason: 'missing_credentials',
                    hasEmail: !!email,
                    hasPassword: !!password
                }
            });

            res.status(400).json({
                success: false,
                error: 'Email and password are required'
            } as ApiResponse);
            return;
        }

        // Authenticate user with enhanced security
        const authResult = await authenticateUser(email, password, ipAddress, userAgent);

        if (!authResult.success) {
            // Handle account lockout
            if (authResult.lockoutInfo?.isLocked) {
                res.status(423).json({
                    success: false,
                    error: 'Account temporarily locked due to multiple failed attempts',
                    lockoutInfo: {
                        remainingTime: authResult.lockoutInfo.remainingTime,
                        lockoutExpires: authResult.lockoutInfo.lockoutExpires
                    }
                } as ApiResponse);
                return;
            }

            // Generic error for failed authentication
            res.status(401).json({
                success: false,
                error: authResult.error || 'Invalid credentials'
            } as ApiResponse);
            return;
        }

        // Create secure session
        const sessionData = await sessionManager.createSession({
            userId: authResult.user!.id,
            ipAddress,
            userAgent
        });

        // Set secure session cookie
        res.cookie('session_token', sessionData.sessionToken, {
            httpOnly: true,
            secure: process.env.NODE_ENV === 'production',
            sameSite: 'strict',
            maxAge: 24 * 60 * 60 * 1000, // 24 hours
            path: '/'
        });

        // Return success response
        res.status(200).json({
            success: true,
            data: {
                user: authResult.user,
                requiresPasswordChange: authResult.requiresPasswordChange,
                sessionToken: sessionData.sessionToken
            }
        } as ApiResponse);

    } catch (error) {
        console.error('Login handler error:', error);

        await SecurityLogger.logSecurityEvent({
            eventType: SecurityEventType.LOGIN_FAILURE,
            email: email || 'unknown',
            ipAddress,
            userAgent,
            success: false,
            errorMessage: 'System error during login',
            details: {
                reason: 'system_error',
                errorType: error instanceof Error ? error.constructor.name : 'UnknownError'
            }
        });

        res.status(500).json({
            success: false,
            error: 'Authentication failed due to system error'
        } as ApiResponse);
    }
}

/**
 * Logout endpoint with proper session cleanup
 * POST /api/auth/logout
 */
export async function logoutHandler(req: Request, res: Response): Promise<void> {
    const sessionToken = req.cookies?.session_token || req.headers.authorization?.replace('Bearer ', '');
    const ipAddress = getClientIp(req);
    const userAgent = getUserAgent(req);

    try {
        if (sessionToken) {
            // Get session info for logging before invalidation
            const sessionValidation = await sessionManager.validateSession(sessionToken, ipAddress);

            if (sessionValidation.isValid && sessionValidation.user) {
                // Log logout event
                await SecurityLogger.logAuthEvent({
                    type: 'LOGOUT',
                    userId: sessionValidation.user.id,
                    email: sessionValidation.user.email,
                    ipAddress,
                    userAgent,
                    timestamp: new Date(),
                    details: {
                        sessionToken: sessionToken.substring(0, 8) + '...',
                        logoutMethod: 'manual'
                    }
                });
            }

            // Invalidate session
            await sessionManager.invalidateSession(sessionToken);
        }

        // Clear session cookie
        res.clearCookie('session_token', {
            httpOnly: true,
            secure: process.env.NODE_ENV === 'production',
            sameSite: 'strict',
            path: '/'
        });

        res.status(200).json({
            success: true,
            message: 'Logged out successfully'
        } as ApiResponse);

    } catch (error) {
        console.error('Logout handler error:', error);

        await SecurityLogger.logSecurityEvent({
            eventType: SecurityEventType.LOGOUT,
            ipAddress,
            userAgent,
            success: false,
            errorMessage: 'System error during logout',
            details: {
                reason: 'system_error',
                hasSessionToken: !!sessionToken
            }
        });

        res.status(500).json({
            success: false,
            error: 'Logout failed due to system error'
        } as ApiResponse);
    }
}

/**
 * Password change endpoint with validation and security
 * POST /api/auth/change-password
 */
export async function changePasswordHandler(req: Request, res: Response): Promise<void> {
    const { currentPassword, newPassword } = req.body;
    const sessionToken = req.cookies?.session_token || req.headers.authorization?.replace('Bearer ', '');
    const ipAddress = getClientIp(req);
    const userAgent = getUserAgent(req);

    try {
        // Validate session
        if (!sessionToken) {
            res.status(401).json({
                success: false,
                error: 'Authentication required'
            } as ApiResponse);
            return;
        }

        const sessionValidation = await sessionManager.validateSession(sessionToken, ipAddress);
        if (!sessionValidation.isValid || !sessionValidation.user) {
            res.status(401).json({
                success: false,
                error: 'Invalid or expired session'
            } as ApiResponse);
            return;
        }

        // Input validation
        if (!currentPassword || !newPassword) {
            await SecurityLogger.logSecurityEvent({
                eventType: SecurityEventType.PASSWORD_CHANGE,
                userId: sessionValidation.user.id,
                email: sessionValidation.user.email,
                ipAddress,
                userAgent,
                success: false,
                errorMessage: 'Missing password fields',
                details: {
                    reason: 'missing_fields',
                    hasCurrentPassword: !!currentPassword,
                    hasNewPassword: !!newPassword
                }
            });

            res.status(400).json({
                success: false,
                error: 'Current password and new password are required'
            } as ApiResponse);
            return;
        }

        // Validate new password complexity
        const passwordValidation = validatePasswordComplexity(newPassword);
        if (!passwordValidation.isValid) {
            await SecurityLogger.logSecurityEvent({
                eventType: SecurityEventType.PASSWORD_CHANGE,
                userId: sessionValidation.user.id,
                email: sessionValidation.user.email,
                ipAddress,
                userAgent,
                success: false,
                errorMessage: 'Password complexity validation failed',
                details: {
                    reason: 'complexity_validation_failed',
                    validationErrors: passwordValidation.errors
                }
            });

            res.status(400).json({
                success: false,
                error: `Password does not meet complexity requirements: ${passwordValidation.errors.join(', ')}`
            } as ApiResponse);
            return;
        }

        // Change password
        const changeResult = await changePassword({
            userId: sessionValidation.user.id,
            currentPassword,
            newPassword,
            ipAddress,
            userAgent,
            currentSessionToken: sessionToken
        });

        if (!changeResult.success) {
            res.status(400).json({
                success: false,
                error: changeResult.error || 'Failed to change password'
            } as ApiResponse);
            return;
        }

        // Invalidate all other sessions except current one
        await sessionManager.invalidateSessionsForSecurityEvents(
            sessionValidation.user.id,
            'password_change',
            sessionToken
        );

        res.status(200).json({
            success: true,
            message: 'Password changed successfully. Other sessions have been invalidated.'
        } as ApiResponse);

    } catch (error) {
        console.error('Change password handler error:', error);

        await SecurityLogger.logSecurityEvent({
            eventType: SecurityEventType.PASSWORD_CHANGE,
            ipAddress,
            userAgent,
            success: false,
            errorMessage: 'System error during password change',
            details: {
                reason: 'system_error',
                errorType: error instanceof Error ? error.constructor.name : 'UnknownError'
            }
        });

        res.status(500).json({
            success: false,
            error: 'Password change failed due to system error'
        } as ApiResponse);
    }
}

/**
 * Admin user creation endpoint with authorization
 * POST /api/auth/admin-users
 */
export async function createAdminUserHandler(req: Request, res: Response): Promise<void> {
    const { email, firstName, lastName, role, temporaryPassword, requirePasswordChange } = req.body;
    const sessionToken = req.cookies?.session_token || req.headers.authorization?.replace('Bearer ', '');
    const ipAddress = getClientIp(req);
    const userAgent = getUserAgent(req);

    try {
        // Validate session
        if (!sessionToken) {
            res.status(401).json({
                success: false,
                error: 'Authentication required'
            } as ApiResponse);
            return;
        }

        const sessionValidation = await sessionManager.validateSession(sessionToken, ipAddress);
        if (!sessionValidation.isValid || !sessionValidation.user) {
            res.status(401).json({
                success: false,
                error: 'Invalid or expired session'
            } as ApiResponse);
            return;
        }

        // Check if user has permission to create admin users (only SUPER_ADMIN)
        if (sessionValidation.user.role !== 'SUPER_ADMIN') {
            await SecurityLogger.logAdminAction({
                eventType: SecurityEventType.ADMIN_USER_CREATED,
                userId: sessionValidation.user.id,
                email: sessionValidation.user.email,
                ipAddress,
                userAgent,
                resource: 'admin_user',
                action: 'creation_denied',
                success: false,
                errorMessage: 'Insufficient permissions',
                details: {
                    reason: 'permission_denied',
                    userRole: sessionValidation.user.role,
                    targetEmail: email
                }
            });

            res.status(403).json({
                success: false,
                error: 'Insufficient permissions to create admin users'
            } as ApiResponse);
            return;
        }

        // Input validation
        if (!email || !firstName || !lastName || !role) {
            res.status(400).json({
                success: false,
                error: 'Email, first name, last name, and role are required'
            } as ApiResponse);
            return;
        }

        if (!['DEV', 'MARKETING', 'SALES'].includes(role)) {
            res.status(400).json({
                success: false,
                error: 'Role must be DEV, MARKETING, or SALES'
            } as ApiResponse);
            return;
        }

        // Create admin user
        const createResult = await createAdminUser(
            sessionValidation.user.id,
            {
                email,
                firstName,
                lastName,
                role,
                temporaryPassword,
                requirePasswordChange
            },
            ipAddress,
            userAgent
        );

        if (!createResult.success) {
            res.status(400).json({
                success: false,
                error: createResult.error || 'Failed to create admin user'
            } as ApiResponse);
            return;
        }

        res.status(201).json({
            success: true,
            data: {
                user: createResult.user,
                temporaryPassword: createResult.temporaryPassword
            },
            message: 'Admin user created successfully'
        } as ApiResponse);

    } catch (error) {
        console.error('Create admin user handler error:', error);

        await SecurityLogger.logAdminAction({
            eventType: SecurityEventType.ADMIN_USER_CREATED,
            ipAddress,
            userAgent,
            resource: 'admin_user',
            action: 'creation_error',
            success: false,
            errorMessage: 'System error during admin user creation',
            details: {
                reason: 'system_error',
                targetEmail: email,
                errorType: error instanceof Error ? error.constructor.name : 'UnknownError'
            }
        });

        res.status(500).json({
            success: false,
            error: 'Admin user creation failed due to system error'
        } as ApiResponse);
    }
}

/**
 * Get current user profile
 * GET /api/auth/profile
 */
export async function getProfileHandler(req: Request, res: Response): Promise<void> {
    const sessionToken = req.cookies?.session_token || req.headers.authorization?.replace('Bearer ', '');
    const ipAddress = getClientIp(req);

    try {
        if (!sessionToken) {
            res.status(401).json({
                success: false,
                error: 'Authentication required'
            } as ApiResponse);
            return;
        }

        const sessionValidation = await sessionManager.validateSession(sessionToken, ipAddress);
        if (!sessionValidation.isValid || !sessionValidation.user) {
            res.status(401).json({
                success: false,
                error: 'Invalid or expired session'
            } as ApiResponse);
            return;
        }

        res.status(200).json({
            success: true,
            data: {
                user: sessionValidation.user
            }
        } as ApiResponse);

    } catch (error) {
        console.error('Get profile handler error:', error);

        res.status(500).json({
            success: false,
            error: 'Failed to get user profile'
        } as ApiResponse);
    }
}

/**
 * Request password reset
 * POST /api/auth/request-password-reset
 */
export async function requestPasswordResetHandler(req: Request, res: Response): Promise<void> {
    const { email } = req.body;
    const ipAddress = getClientIp(req);
    const userAgent = getUserAgent(req);

    try {
        if (!email) {
            res.status(400).json({
                success: false,
                error: 'Email is required'
            } as ApiResponse);
            return;
        }

        // Request password reset (always returns success to prevent user enumeration)
        const resetResult = await generatePasswordResetToken({
            email,
            ipAddress,
            userAgent
        });

        // Always return success response to prevent user enumeration
        res.status(200).json({
            success: true,
            message: 'If the email exists in our system, a password reset link has been sent.'
        } as ApiResponse);

    } catch (error) {
        console.error('Request password reset handler error:', error);

        res.status(500).json({
            success: false,
            error: 'Password reset request failed due to system error'
        } as ApiResponse);
    }
}

/**
 * Reset password with token
 * POST /api/auth/reset-password
 */
export async function resetPasswordHandler(req: Request, res: Response): Promise<void> {
    const { token, newPassword } = req.body;
    const ipAddress = getClientIp(req);
    const userAgent = getUserAgent(req);

    try {
        if (!token || !newPassword) {
            res.status(400).json({
                success: false,
                error: 'Reset token and new password are required'
            } as ApiResponse);
            return;
        }

        // Validate new password complexity
        const passwordValidation = validatePasswordComplexity(newPassword);
        if (!passwordValidation.isValid) {
            res.status(400).json({
                success: false,
                error: `Password does not meet complexity requirements: ${passwordValidation.errors.join(', ')}`
            } as ApiResponse);
            return;
        }

        // Reset password
        const resetResult = await resetPasswordWithToken(token, newPassword, ipAddress, userAgent);

        if (!resetResult.success) {
            res.status(400).json({
                success: false,
                error: resetResult.error || 'Failed to reset password'
            } as ApiResponse);
            return;
        }

        res.status(200).json({
            success: true,
            message: 'Password reset successfully'
        } as ApiResponse);

    } catch (error) {
        console.error('Reset password handler error:', error);

        res.status(500).json({
            success: false,
            error: 'Password reset failed due to system error'
        } as ApiResponse);
    }
}

/**
 * Verify 2FA token during login
 * POST /api/auth/verify-2fa
 */
export async function verify2FAHandler(req: Request, res: Response): Promise<void> {
    const { token } = req.body;
    const ipAddress = getClientIp(req);
    const userAgent = getUserAgent(req);

    try {
        if (!token) {
            res.status(400).json({
                success: false,
                error: '2FA token is required'
            } as ApiResponse);
            return;
        }

        // Get pending 2FA session from temporary storage (you might want to use Redis for this)
        // For now, we'll assume the user ID is stored in a temporary session
        const sessionToken = req.cookies?.temp_session_token;
        if (!sessionToken) {
            res.status(401).json({
                success: false,
                error: 'No pending 2FA session found'
            } as ApiResponse);
            return;
        }

        // Validate the temporary session and get user info
        const tempSession = await sessionManager.validateSession(sessionToken, ipAddress);
        if (!tempSession.isValid || !tempSession.user) {
            res.status(401).json({
                success: false,
                error: 'Invalid or expired 2FA session'
            } as ApiResponse);
            return;
        }

        const user = tempSession.user;

        // Verify the 2FA token
        const isValid = speakeasy.totp.verify({
            secret: user.two_factor_secret!,
            encoding: 'base32',
            token: token,
            window: 2 // Allow 2 time steps of variance
        });

        if (!isValid) {
            await SecurityLogger.logSecurityEvent({
                eventType: SecurityEventType.LOGIN_FAILURE,
                userId: user.id,
                email: user.email,
                ipAddress,
                userAgent,
                success: false,
                errorMessage: 'Invalid 2FA token',
                details: {
                    reason: 'invalid_2fa_token'
                }
            });

            res.status(401).json({
                success: false,
                error: 'Invalid 2FA token'
            } as ApiResponse);
            return;
        }

        // Create a full session
        const sessionData = await sessionManager.createSession({
            userId: user.id,
            ipAddress,
            userAgent
        });

        // Clear the temporary session cookie
        res.clearCookie('temp_session_token');

        // Set the full session cookie
        res.cookie('session_token', sessionData.sessionToken, {
            httpOnly: true,
            secure: process.env.NODE_ENV === 'production',
            sameSite: 'strict',
            maxAge: 24 * 60 * 60 * 1000, // 24 hours
            path: '/'
        });

        await SecurityLogger.logSecurityEvent({
            eventType: SecurityEventType.LOGIN_SUCCESS,
            userId: user.id,
            email: user.email,
            ipAddress,
            userAgent,
            success: true,
            details: {
                method: '2fa_verification'
            }
        });

        res.status(200).json({
            success: true,
            data: {
                user: user,
                token: sessionData.sessionToken
            }
        } as ApiResponse);

    } catch (error) {
        console.error('2FA verification error:', error);

        await SecurityLogger.logSecurityEvent({
            eventType: SecurityEventType.LOGIN_FAILURE,
            ipAddress,
            userAgent,
            success: false,
            errorMessage: 'System error during 2FA verification',
            details: {
                reason: 'system_error',
                errorType: error instanceof Error ? error.constructor.name : 'UnknownError'
            }
        });

        res.status(500).json({
            success: false,
            error: '2FA verification failed due to system error'
        } as ApiResponse);
    }
}

/**
 * Enable 2FA for user account
 * POST /api/auth/enable-2fa
 */
export async function enable2FAHandler(req: Request, res: Response): Promise<void> {
    const sessionToken = req.cookies?.session_token || req.headers.authorization?.replace('Bearer ', '');
    const ipAddress = getClientIp(req);
    const userAgent = getUserAgent(req);

    try {
        if (!sessionToken) {
            res.status(401).json({
                success: false,
                error: 'Authentication required'
            } as ApiResponse);
            return;
        }

        const sessionValidation = await sessionManager.validateSession(sessionToken, ipAddress);
        if (!sessionValidation.isValid || !sessionValidation.user) {
            res.status(401).json({
                success: false,
                error: 'Invalid or expired session'
            } as ApiResponse);
            return;
        }

        const user = sessionValidation.user;

        // Check if 2FA is already enabled
        if (user.two_factor_enabled) {
            res.status(400).json({
                success: false,
                error: '2FA is already enabled for this account'
            } as ApiResponse);
            return;
        }

        // Generate a new secret
        const secret = speakeasy.generateSecret({
            name: `GuardGeo (${user.email})`,
            issuer: 'GuardGeo'
        });

        // Generate QR code
        const qrCodeUrl = await QRCode.toDataURL(secret.otpauth_url!);

        // Store the secret temporarily (don't enable 2FA until verified)
        await prisma.user.update({
            where: { id: user.id },
            data: {
                two_factor_secret: secret.base32,
                updated_at: new Date()
            }
        });

        await SecurityLogger.logSecurityEvent({
            eventType: SecurityEventType.ADMIN_USER_UPDATED,
            userId: user.id,
            email: user.email,
            ipAddress,
            userAgent,
            success: true,
            details: {
                action: '2fa_setup_initiated'
            }
        });

        res.status(200).json({
            success: true,
            data: {
                qr_code: qrCodeUrl,
                secret: secret.base32
            }
        } as ApiResponse);

    } catch (error) {
        console.error('Enable 2FA error:', error);

        await SecurityLogger.logSecurityEvent({
            eventType: SecurityEventType.ADMIN_USER_UPDATED,
            ipAddress,
            userAgent,
            success: false,
            errorMessage: 'System error during 2FA setup',
            details: {
                reason: 'system_error',
                errorType: error instanceof Error ? error.constructor.name : 'UnknownError'
            }
        });

        res.status(500).json({
            success: false,
            error: '2FA setup failed due to system error'
        } as ApiResponse);
    }
}

/**
 * Verify 2FA token and complete setup
 * POST /api/auth/2fa/verify
 */
export async function verify2FASetupHandler(req: Request, res: Response): Promise<void> {
    const { token, secret } = req.body;
    const sessionToken = req.cookies?.session_token || req.headers.authorization?.replace('Bearer ', '');
    const ipAddress = getClientIp(req);
    const userAgent = getUserAgent(req);

    try {
        if (!token || !secret) {
            res.status(400).json({
                success: false,
                error: '2FA token and secret are required'
            } as ApiResponse);
            return;
        }

        if (!sessionToken) {
            res.status(401).json({
                success: false,
                error: 'Authentication required'
            } as ApiResponse);
            return;
        }

        const sessionValidation = await sessionManager.validateSession(sessionToken, ipAddress);
        if (!sessionValidation.isValid || !sessionValidation.user) {
            res.status(401).json({
                success: false,
                error: 'Invalid or expired session'
            } as ApiResponse);
            return;
        }

        const user = sessionValidation.user;

        // Verify the token against the provided secret
        const isValid = speakeasy.totp.verify({
            secret: secret,
            encoding: 'base32',
            token: token,
            window: 2
        });

        if (!isValid) {
            await SecurityLogger.logSecurityEvent({
                eventType: SecurityEventType.ADMIN_USER_UPDATED,
                userId: user.id,
                email: user.email,
                ipAddress,
                userAgent,
                success: false,
                errorMessage: 'Invalid 2FA token during setup',
                details: {
                    action: '2fa_setup_verification_failed'
                }
            });

            res.status(400).json({
                success: false,
                error: 'Invalid 2FA token'
            } as ApiResponse);
            return;
        }

        // Enable 2FA for the user
        await prisma.user.update({
            where: { id: user.id },
            data: {
                two_factor_enabled: true,
                two_factor_secret: secret,
                updated_at: new Date()
            }
        });

        await SecurityLogger.logSecurityEvent({
            eventType: SecurityEventType.ADMIN_USER_UPDATED,
            userId: user.id,
            email: user.email,
            ipAddress,
            userAgent,
            success: true,
            details: {
                action: '2fa_enabled'
            }
        });

        res.status(200).json({
            success: true,
            message: '2FA has been successfully enabled'
        } as ApiResponse);

    } catch (error) {
        console.error('2FA setup verification error:', error);

        await SecurityLogger.logSecurityEvent({
            eventType: SecurityEventType.ADMIN_USER_UPDATED,
            ipAddress,
            userAgent,
            success: false,
            errorMessage: 'System error during 2FA setup verification',
            details: {
                reason: 'system_error',
                errorType: error instanceof Error ? error.constructor.name : 'UnknownError'
            }
        });

        res.status(500).json({
            success: false,
            error: '2FA setup verification failed due to system error'
        } as ApiResponse);
    }
}