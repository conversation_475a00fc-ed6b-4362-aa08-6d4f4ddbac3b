import { Request, Response } from 'express';
import { sessionManager } from '../lib/session-manager';
import { SecurityLogger, SecurityEventType } from '../lib/security-logger';
import { ApiResponse } from '../types';

// Helper function to get client IP
function getClientIp(req: Request): string {
    const forwarded = req.headers['x-forwarded-for'];
    const realIp = req.headers['x-real-ip'];
    const cfConnectingIp = req.headers['cf-connecting-ip'];

    if (typeof forwarded === 'string') {
        return forwarded.split(',')[0].trim();
    }

    if (typeof realIp === 'string') {
        return realIp;
    }

    if (typeof cfConnectingIp === 'string') {
        return cfConnectingIp;
    }

    return req.connection?.remoteAddress ||
        req.socket?.remoteAddress ||
        '127.0.0.1';
}

// Helper function to get user agent
function getUserAgent(req: Request): string {
    return req.headers['user-agent'] || 'Unknown';
}

/**
 * Session validation endpoint for frontend use
 * GET /api/sessions/validate
 */
export async function validateSessionHandler(req: Request, res: Response): Promise<void> {
    const sessionToken = req.cookies?.session_token || req.headers.authorization?.replace('Bearer ', '');
    const ipAddress = getClientIp(req);
    const userAgent = getUserAgent(req);

    try {
        if (!sessionToken) {
            res.status(401).json({
                success: false,
                error: 'No session token provided'
            } as ApiResponse);
            return;
        }

        const sessionValidation = await sessionManager.validateSession(sessionToken, ipAddress);

        if (!sessionValidation.isValid) {
            // Log session validation failure
            await SecurityLogger.logSessionEvent({
                type: 'SESSION_VALIDATION_FAILED',
                sessionToken: sessionToken.substring(0, 8) + '...',
                ipAddress,
                userAgent,
                timestamp: new Date(),
                details: {
                    reason: sessionValidation.securityAlert || 'Invalid session',
                    validationAttempt: true
                }
            });

            res.status(401).json({
                success: false,
                error: 'Invalid or expired session',
                securityAlert: sessionValidation.securityAlert
            } as ApiResponse);
            return;
        }

        // Log successful session validation
        await SecurityLogger.logSessionEvent({
            type: 'SESSION_VALIDATED',
            userId: sessionValidation.user?.id,
            sessionToken: sessionToken.substring(0, 8) + '...',
            ipAddress,
            userAgent,
            timestamp: new Date(),
            details: {
                requiresRefresh: sessionValidation.requiresRefresh,
                userRole: sessionValidation.user?.role
            }
        });

        res.status(200).json({
            success: true,
            data: {
                user: sessionValidation.user,
                requiresRefresh: sessionValidation.requiresRefresh,
                session: {
                    expiresAt: sessionValidation.session?.expiresAt,
                    ipAddress: sessionValidation.session?.ipAddress,
                    deviceFingerprint: sessionValidation.session?.deviceFingerprint
                }
            }
        } as ApiResponse);

    } catch (error) {
        console.error('Session validation handler error:', error);

        await SecurityLogger.logSessionEvent({
            type: 'SESSION_VALIDATION_ERROR',
            sessionToken: sessionToken ? sessionToken.substring(0, 8) + '...' : 'none',
            ipAddress,
            userAgent,
            timestamp: new Date(),
            details: {
                errorType: error instanceof Error ? error.constructor.name : 'UnknownError',
                errorMessage: error instanceof Error ? error.message : 'Unknown error'
            }
        });

        res.status(500).json({
            success: false,
            error: 'Session validation failed due to system error'
        } as ApiResponse);
    }
}

/**
 * Session refresh endpoint with security validation
 * POST /api/sessions/refresh
 */
export async function refreshSessionHandler(req: Request, res: Response): Promise<void> {
    const sessionToken = req.cookies?.session_token || req.headers.authorization?.replace('Bearer ', '');
    const ipAddress = getClientIp(req);
    const userAgent = getUserAgent(req);

    try {
        if (!sessionToken) {
            res.status(401).json({
                success: false,
                error: 'No session token provided'
            } as ApiResponse);
            return;
        }

        // First validate the session
        const sessionValidation = await sessionManager.validateSession(sessionToken, ipAddress);

        if (!sessionValidation.isValid || !sessionValidation.user) {
            await SecurityLogger.logSessionEvent({
                type: 'SESSION_REFRESH_FAILED',
                sessionToken: sessionToken.substring(0, 8) + '...',
                ipAddress,
                userAgent,
                timestamp: new Date(),
                details: {
                    reason: sessionValidation.securityAlert || 'Invalid session for refresh',
                    refreshAttempt: true
                }
            });

            res.status(401).json({
                success: false,
                error: 'Invalid or expired session'
            } as ApiResponse);
            return;
        }

        // Refresh the session
        const refreshedSession = await sessionManager.refreshSession(sessionToken);

        if (!refreshedSession) {
            await SecurityLogger.logSessionEvent({
                type: 'SESSION_REFRESH_FAILED',
                userId: sessionValidation.user.id,
                sessionToken: sessionToken.substring(0, 8) + '...',
                ipAddress,
                userAgent,
                timestamp: new Date(),
                details: {
                    reason: 'Failed to refresh session',
                    userId: sessionValidation.user.id
                }
            });

            res.status(400).json({
                success: false,
                error: 'Failed to refresh session'
            } as ApiResponse);
            return;
        }

        // Update session cookie with new expiration
        res.cookie('session_token', refreshedSession.sessionToken, {
            httpOnly: true,
            secure: process.env.NODE_ENV === 'production',
            sameSite: 'strict',
            maxAge: 24 * 60 * 60 * 1000, // 24 hours
            path: '/'
        });

        // Log successful session refresh
        await SecurityLogger.logSessionEvent({
            type: 'SESSION_REFRESHED',
            userId: sessionValidation.user.id,
            sessionToken: sessionToken.substring(0, 8) + '...',
            ipAddress,
            userAgent,
            timestamp: new Date(),
            details: {
                newExpiresAt: refreshedSession.expiresAt,
                userId: sessionValidation.user.id,
                userRole: sessionValidation.user.role
            }
        });

        res.status(200).json({
            success: true,
            data: {
                sessionToken: refreshedSession.sessionToken,
                expiresAt: refreshedSession.expiresAt,
                user: sessionValidation.user
            },
            message: 'Session refreshed successfully'
        } as ApiResponse);

    } catch (error) {
        console.error('Session refresh handler error:', error);

        await SecurityLogger.logSessionEvent({
            type: 'SESSION_REFRESH_ERROR',
            sessionToken: sessionToken ? sessionToken.substring(0, 8) + '...' : 'none',
            ipAddress,
            userAgent,
            timestamp: new Date(),
            details: {
                errorType: error instanceof Error ? error.constructor.name : 'UnknownError',
                errorMessage: error instanceof Error ? error.message : 'Unknown error'
            }
        });

        res.status(500).json({
            success: false,
            error: 'Session refresh failed due to system error'
        } as ApiResponse);
    }
}

/**
 * Get user session listing endpoint for security monitoring
 * GET /api/sessions/list
 */
export async function getUserSessionsHandler(req: Request, res: Response): Promise<void> {
    const sessionToken = req.cookies?.session_token || req.headers.authorization?.replace('Bearer ', '');
    const ipAddress = getClientIp(req);
    const userAgent = getUserAgent(req);

    try {
        if (!sessionToken) {
            res.status(401).json({
                success: false,
                error: 'Authentication required'
            } as ApiResponse);
            return;
        }

        const sessionValidation = await sessionManager.validateSession(sessionToken, ipAddress);

        if (!sessionValidation.isValid || !sessionValidation.user) {
            res.status(401).json({
                success: false,
                error: 'Invalid or expired session'
            } as ApiResponse);
            return;
        }

        // Get all active sessions for the user
        const userSessions = await sessionManager.getUserActiveSessions(sessionValidation.user.id);

        // Format sessions for frontend (remove sensitive data)
        const formattedSessions = userSessions.map(session => ({
            sessionId: session.sessionToken.substring(0, 8) + '...',
            ipAddress: session.ipAddress,
            userAgent: session.userAgent,
            location: session.location,
            expiresAt: session.expiresAt,
            isCurrent: session.sessionToken === sessionToken,
            deviceFingerprint: session.deviceFingerprint?.substring(0, 8) + '...',
            securityFlags: session.securityFlags
        }));

        // Log session listing access
        await SecurityLogger.logSessionEvent({
            type: 'SESSION_LIST_ACCESSED',
            userId: sessionValidation.user.id,
            sessionToken: sessionToken.substring(0, 8) + '...',
            ipAddress,
            userAgent,
            timestamp: new Date(),
            details: {
                totalSessions: userSessions.length,
                userId: sessionValidation.user.id
            }
        });

        res.status(200).json({
            success: true,
            data: {
                sessions: formattedSessions,
                totalSessions: userSessions.length,
                currentSessionId: sessionToken.substring(0, 8) + '...'
            }
        } as ApiResponse);

    } catch (error) {
        console.error('Get user sessions handler error:', error);

        res.status(500).json({
            success: false,
            error: 'Failed to get user sessions'
        } as ApiResponse);
    }
}

/**
 * Revoke single session endpoint
 * DELETE /api/sessions/:sessionId
 */
export async function revokeSessionHandler(req: Request, res: Response): Promise<void> {
    const { sessionId } = req.params;
    const currentSessionToken = req.cookies?.session_token || req.headers.authorization?.replace('Bearer ', '');
    const ipAddress = getClientIp(req);
    const userAgent = getUserAgent(req);

    try {
        if (!currentSessionToken) {
            res.status(401).json({
                success: false,
                error: 'Authentication required'
            } as ApiResponse);
            return;
        }

        const sessionValidation = await sessionManager.validateSession(currentSessionToken, ipAddress);

        if (!sessionValidation.isValid || !sessionValidation.user) {
            res.status(401).json({
                success: false,
                error: 'Invalid or expired session'
            } as ApiResponse);
            return;
        }

        if (!sessionId) {
            res.status(400).json({
                success: false,
                error: 'Session ID is required'
            } as ApiResponse);
            return;
        }

        // Get user's active sessions to find the full session token
        const userSessions = await sessionManager.getUserActiveSessions(sessionValidation.user.id);
        const targetSession = userSessions.find(session =>
            session.sessionToken.startsWith(sessionId.replace('...', ''))
        );

        if (!targetSession) {
            res.status(404).json({
                success: false,
                error: 'Session not found or already expired'
            } as ApiResponse);
            return;
        }

        // Prevent users from revoking their current session through this endpoint
        if (targetSession.sessionToken === currentSessionToken) {
            res.status(400).json({
                success: false,
                error: 'Cannot revoke current session. Use logout instead.'
            } as ApiResponse);
            return;
        }

        // Revoke the session
        await sessionManager.invalidateSession(targetSession.sessionToken);

        // Log session revocation
        await SecurityLogger.logSessionEvent({
            type: 'SESSION_REVOKED',
            userId: sessionValidation.user.id,
            sessionToken: currentSessionToken.substring(0, 8) + '...',
            ipAddress,
            userAgent,
            timestamp: new Date(),
            details: {
                revokedSessionId: sessionId,
                revokedSessionIp: targetSession.ipAddress,
                revokedByUser: sessionValidation.user.id,
                reason: 'manual_revocation'
            }
        });

        res.status(200).json({
            success: true,
            message: 'Session revoked successfully'
        } as ApiResponse);

    } catch (error) {
        console.error('Revoke session handler error:', error);

        res.status(500).json({
            success: false,
            error: 'Failed to revoke session'
        } as ApiResponse);
    }
}

/**
 * Bulk session revocation endpoint (revoke all other sessions)
 * POST /api/sessions/revoke-others
 */
export async function revokeOtherSessionsHandler(req: Request, res: Response): Promise<void> {
    const currentSessionToken = req.cookies?.session_token || req.headers.authorization?.replace('Bearer ', '');
    const ipAddress = getClientIp(req);
    const userAgent = getUserAgent(req);

    try {
        if (!currentSessionToken) {
            res.status(401).json({
                success: false,
                error: 'Authentication required'
            } as ApiResponse);
            return;
        }

        const sessionValidation = await sessionManager.validateSession(currentSessionToken, ipAddress);

        if (!sessionValidation.isValid || !sessionValidation.user) {
            res.status(401).json({
                success: false,
                error: 'Invalid or expired session'
            } as ApiResponse);
            return;
        }

        // Revoke all other sessions except the current one
        const revokedCount = await sessionManager.invalidateUserSessions(
            sessionValidation.user.id,
            currentSessionToken
        );

        // Log bulk session revocation
        await SecurityLogger.logSessionEvent({
            type: 'BULK_SESSION_REVOKED',
            userId: sessionValidation.user.id,
            sessionToken: currentSessionToken.substring(0, 8) + '...',
            ipAddress,
            userAgent,
            timestamp: new Date(),
            details: {
                revokedCount,
                userId: sessionValidation.user.id,
                reason: 'bulk_revocation_by_user'
            }
        });

        res.status(200).json({
            success: true,
            data: {
                revokedCount
            },
            message: `Successfully revoked ${revokedCount} other sessions`
        } as ApiResponse);

    } catch (error) {
        console.error('Revoke other sessions handler error:', error);

        res.status(500).json({
            success: false,
            error: 'Failed to revoke other sessions'
        } as ApiResponse);
    }
}

/**
 * Session security metrics endpoint (for admin monitoring)
 * GET /api/sessions/security-metrics
 */
export async function getSessionSecurityMetricsHandler(req: Request, res: Response): Promise<void> {
    const currentSessionToken = req.cookies?.session_token || req.headers.authorization?.replace('Bearer ', '');
    const ipAddress = getClientIp(req);
    const userAgent = getUserAgent(req);

    try {
        if (!currentSessionToken) {
            res.status(401).json({
                success: false,
                error: 'Authentication required'
            } as ApiResponse);
            return;
        }

        const sessionValidation = await sessionManager.validateSession(currentSessionToken, ipAddress);

        if (!sessionValidation.isValid || !sessionValidation.user) {
            res.status(401).json({
                success: false,
                error: 'Invalid or expired session'
            } as ApiResponse);
            return;
        }

        // Check if user has permission to view security metrics (dev and above only)
        if (!['SUPER_ADMIN', 'DEV'].includes(sessionValidation.user.role)) {
            res.status(403).json({
                success: false,
                error: 'Insufficient permissions to view security metrics'
            } as ApiResponse);
            return;
        }

        // Get session security metrics
        const metrics = await sessionManager.getSessionSecurityMetrics();

        // Log security metrics access
        await SecurityLogger.logAdminAction({
            eventType: SecurityEventType.SECURITY_AUDIT_ACCESSED,
            userId: sessionValidation.user.id,
            email: sessionValidation.user.email,
            ipAddress,
            userAgent,
            resource: 'session_security_metrics',
            action: 'view',
            success: true,
            details: {
                metricsRequested: 'session_security',
                adminRole: sessionValidation.user.role
            }
        });

        res.status(200).json({
            success: true,
            data: metrics
        } as ApiResponse);

    } catch (error) {
        console.error('Get session security metrics handler error:', error);

        res.status(500).json({
            success: false,
            error: 'Failed to get session security metrics'
        } as ApiResponse);
    }
}

/**
 * Session anomaly detection endpoint
 * GET /api/sessions/anomalies
 */
export async function getSessionAnomaliesHandler(req: Request, res: Response): Promise<void> {
    const currentSessionToken = req.cookies?.session_token || req.headers.authorization?.replace('Bearer ', '');
    const ipAddress = getClientIp(req);
    const userAgent = getUserAgent(req);

    try {
        if (!currentSessionToken) {
            res.status(401).json({
                success: false,
                error: 'Authentication required'
            } as ApiResponse);
            return;
        }

        const sessionValidation = await sessionManager.validateSession(currentSessionToken, ipAddress);

        if (!sessionValidation.isValid || !sessionValidation.user) {
            res.status(401).json({
                success: false,
                error: 'Invalid or expired session'
            } as ApiResponse);
            return;
        }

        // Get session anomalies for the current user
        const anomalies = await sessionManager.detectSessionAnomalies(sessionValidation.user.id);

        // Log anomaly detection access
        await SecurityLogger.logSessionEvent({
            type: 'SESSION_ANOMALY_CHECK',
            userId: sessionValidation.user.id,
            sessionToken: currentSessionToken.substring(0, 8) + '...',
            ipAddress,
            userAgent,
            timestamp: new Date(),
            details: {
                anomaliesFound: anomalies.length,
                userId: sessionValidation.user.id
            }
        });

        res.status(200).json({
            success: true,
            data: {
                anomalies,
                totalAnomalies: anomalies.length,
                userId: sessionValidation.user.id
            }
        } as ApiResponse);

    } catch (error) {
        console.error('Get session anomalies handler error:', error);

        res.status(500).json({
            success: false,
            error: 'Failed to get session anomalies'
        } as ApiResponse);
    }
}